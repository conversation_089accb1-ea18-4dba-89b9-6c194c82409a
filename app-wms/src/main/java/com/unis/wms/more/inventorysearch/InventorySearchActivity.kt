package com.unis.wms.more.inventorysearch

import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.view.View
import android.widget.AdapterView
import android.widget.ArrayAdapter
import androidx.recyclerview.widget.LinearLayoutManager
import com.customer.widget.RecyclerViewItemSpace
import com.customer.widget.common.CallbackDialogV1
import com.customer.widget.core.ActivityBundleHolder
import com.customer.widget.extensions.setGone
import com.customer.widget.extensions.setVisible
import com.customer.widget.extensions.setVisibleOrGone
import com.google.firebase.analytics.FirebaseAnalytics.Param.ITEM_NAME
import com.unis.platform.inventory.model.*
import com.unis.platform.item.model.ItemEntity
import com.unis.platform.item.model.ItemQueryEntity
import com.unis.platform.item.model.ItemStatusEntity
import com.unis.reactivemvi.mvi.ReactiveActivity
import com.unis.reactivemvi.mvi.ReactiveViewScope
import com.unis.reactivemvi.mvi.onEvent
import com.unis.reactivemvi.mvvm.kotlin.extensions.UniversalActivityParam
import com.unis.wms.R
import com.unis.wms.common.IntentKey
import com.unis.wms.databinding.ActivityInventorySearchBinding
import com.unis.wms.more.inventorysearch.view.InventoryViewActivity
import java.io.Serializable

class InventorySearchActivity : ReactiveActivity<InventorySearchViewModel, InventorySearchUiState, ActivityInventorySearchBinding>() {

    private val itemAdapter:InventorySearchItemAdapter by lazy { InventorySearchItemAdapter() }
    private val lpAdapter:InventorySearchLpLocationAdapter by lazy { InventorySearchLpLocationAdapter() }
    private val locationAdapter:InventorySearchLpLocationAdapter by lazy { InventorySearchLpLocationAdapter() }

    companion object {
        fun startActivity(context: Context, inventorySearchConfig: InventorySearchConfig?) {
            Intent(context, InventorySearchActivity::class.java).apply {
                putExtra(InventorySearchConfig.TAG, inventorySearchConfig)
                context.startActivity(this)
            }
        }
    }

    override fun createViewModel(): InventorySearchViewModel {
        val serializableExtra = intent.getSerializableExtra(InventorySearchConfig.TAG)
        val inventorySearchConfig = if (serializableExtra is InventorySearchConfig) {
            serializableExtra
        } else {
            null
        }
        return InventorySearchViewModel(InventorySearchDataState(inventorySearchConfig = inventorySearchConfig))
    }

    override fun initView(savedInstanceState: Bundle?) {
        binding.apply {
            initToolBar(toolbar, R.string.inventory_search)
            val spinnerAdapter = ArrayAdapter(
                this@InventorySearchActivity,
                R.layout.item_spinner_text, InventoryStatusEntity.values().map { v -> v.displayName() }
            )
            spinnerAdapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item)
            inventoryStatusSpinner.adapter = spinnerAdapter
            inventoryStatusSpinner.setSelection(getSelectionPosition(InventoryStatusEntity.AVAILABLE))
            inventoryStatusSpinner.onItemSelectedListener = object : AdapterView.OnItemSelectedListener{
                override fun onItemSelected(parent: AdapterView<*>?, view: View?, position: Int, id: Long) {
                    val inventoryStatus = InventoryStatusEntity.values()[position]
                    viewModel.setInventoryStatus(inventoryStatus)
                }

                override fun onNothingSelected(parent: AdapterView<*>?) {
                }
            }
            recyclerView.layoutManager = LinearLayoutManager(this@InventorySearchActivity)
            recyclerView.addItemDecoration(RecyclerViewItemSpace(this@InventorySearchActivity))
            recyclerView.adapter = itemAdapter
            itemAdapter.setOnItemClickListener { _, _, position ->
                startInventoryViewActivity(itemAdapter.getItem(position)?.inventories)
            }
            byItemCheckBox.setOnCheckedChangeListener { _, isChecked ->
                if (isChecked) {
                    showSearchByItemView()
                }
            }
            byLpCheckBox.setOnCheckedChangeListener { _, isChecked ->
                if (isChecked) {
                    showSearchByLpView()
                }
            }
            byLocationCheckBox.setOnCheckedChangeListener { _, isChecked ->
                if (isChecked) {
                    showSearchByLocationView()
                }
            }
            searchScanner.setScanEvent { _, data ->
                if (data.isNullOrEmpty()) {
                    return@setScanEvent
                }
                inventorySearchHandle(data)
            }
            onShowSearchItemInventoryResultEvent()
            onShowSearchLocationInventoryResultEvent()

            val itemId = viewModel.getInventorySearchConfig()?.itemId
            if (!itemId.isNullOrEmpty()) {
                byItemCheckBox.isChecked = true
                inventorySearchHandle(itemId)
            }
        }
    }

    private fun inventorySearchHandle(data: String) {
        binding.apply {
            when {
                byItemCheckBox.isChecked && !byItemAka.isChecked -> {
                    val itemSearchEntity = generateItemSearchEntity(data)
                    viewModel.searchItem(itemSearchEntity) {
                        CallbackDialogV1.showSingleChoice(
                            context = this@InventorySearchActivity,
                            items = it,
                            itemTitleMapper = { itemEntity ->
                                val description = if (itemEntity.description.isNullOrEmpty()) "" else "(${itemEntity.description})"
                                "${itemEntity.name}$description"
                            })
                    }
                }

                byItemCheckBox.isChecked && byItemAka.isChecked -> {
                    viewModel.searchItemAka(data) {
                        CallbackDialogV1.showSingleChoice(
                            context = this@InventorySearchActivity,
                            items = it,
                            itemTitleMapper = { itemEntity ->
                                val description = if (itemEntity.description.isNullOrEmpty()) "" else "(${itemEntity.description})"
                                "${itemEntity.name}$description"
                            })
                    }
                }
                byLpCheckBox.isChecked -> {
                    val inventoryQueryEntity = InventoryQueryEntity().apply {
                        this.status = viewModel.getInventoryStatus()
                        this.lpId = data
                        val customerId = viewModel.getInventorySearchConfig()?.customerId
                        if (!customerId.isNullOrEmpty()) {
                            this.customerId = customerId
                        }
                    }
                    viewModel.searchInventory(inventoryQueryEntity) {
                        showLpInventory(it)
                    }
                }
                byLocationCheckBox.isChecked -> {
                    viewModel.searchLocation(data) {
                        CallbackDialogV1.showSingleChoice(
                            context= this@InventorySearchActivity,
                            items = it,
                            itemTitleMapper = { location -> location.name })
                    }
                }
            }
        }
    }

    private fun generateItemSearchEntity(data: String): ItemQueryEntity {
        binding.apply {
            return ItemQueryEntity().apply {
                when {
                    byItemName.isChecked -> {
                        this.keyword = data
                    }
                    byItemUpc.isChecked -> {
                        this.upcCode = data
                    }
                    byItemUpcCase.isChecked -> {
                        this.upcCodeCase = data
                    }
                    byDescription.isChecked -> {
                        this.description = data
                    }
                    byAbbreviation.isChecked -> {
                        this.abbreviation = data
                    }
                }
                val customerId = viewModel.getInventorySearchConfig()?.customerId
                if (!customerId.isNullOrEmpty()) {
                    this.customerId = customerId
                }
                this.status = ItemStatusEntity.ACTIVE
                this.pageSize = 200
            }
        }
    }

    private fun showSearchByItemView() {
        binding.apply {
            searchScanner.setInputFocus()
            searchScanner.setHintText(R.string.hint_scan_item_upc_aka)
            recyclerView.adapter = itemAdapter
            rgByItem.setVisible()
            itemCsl.setVisibleOrGone(viewModel.getItemEntity() != null)
            byLpCheckBox.isChecked = false
            byLocationCheckBox.isChecked = false
        }
    }

    private fun showSearchByLpView() {
        binding.apply {
            searchScanner.setInputFocus()
            searchScanner.setHintText(R.string.text_scan_lp)
            recyclerView.adapter = lpAdapter
            rgByItem.setGone()
            itemCsl.setGone()
            byItemCheckBox.isChecked = false
            byLocationCheckBox.isChecked = false
        }
    }

    private fun showSearchByLocationView() {
        binding.apply {
            searchScanner.setInputFocus()
            searchScanner.setHintText(R.string.hint_scan_location)
            recyclerView.adapter = locationAdapter
            rgByItem.setGone()
            itemCsl.setGone()
            byItemCheckBox.isChecked = false
            byLpCheckBox.isChecked = false
        }
    }

    private fun getSelectionPosition(status: InventoryStatusEntity): Int {
        val inventoryStatus = InventoryStatusEntity.values()
        for (i in inventoryStatus.indices) {
            if (inventoryStatus[i] == status) {
                return i
            }
        }
        return 0
    }

    override fun ReactiveViewScope.subscribeToUiState() {
    }

    private fun startInventoryViewActivity(inventories: List<InventoryEntity>?) {
        inventories?: return
        val intent = Intent()
        val clazz = InventoryViewActivity::class.java
        intent.setClass(this, clazz)
        val param = InventoryViewActivity.Param(inventories = inventories)
        ActivityBundleHolder.pushSerializable(clazz, Pair<String, Serializable>(UniversalActivityParam.TAG, param))
        startActivity(intent)
    }

    private fun onShowSearchItemInventoryResultEvent() = onEvent<InventorySearchEvent.ShowSearchItemInventoryResult> {
        itemEntity?.let {
            showItemInfo(it, generateShowTotalQty(inventoryEntities))
            showItemInventory(inventoryEntities)
        }
    }

    private fun onShowSearchLocationInventoryResultEvent() = onEvent<InventorySearchEvent.ShowSearchLocationInventoryResult> {
        showLocationInventory(inventoryEntities)
    }

    private fun generateShowTotalQty(inventoryEntities: List<InventoryEntity>?): String {
        val groupedByUom = inventoryEntities?.groupBy { v -> v.uomId }
        val totalQtyShowList  = mutableListOf<String>()
        groupedByUom?.forEach { (_, sameUomInventory) ->
            val totalQty = sameUomInventory.sumOf { v -> v.qty ?: 0.0 }
            val totalQtyShow = "$totalQty(${sameUomInventory[0].uomName})"
            totalQtyShowList.add(totalQtyShow)
        }
        return totalQtyShowList.joinToString("")
    }

    private fun showItemInfo(itemEntity: ItemEntity, totalQtyShow: String) {
        binding.apply {
            itemCsl.setVisible()
            itemNameTv.text = itemEntity.name
            upcTv.text = itemEntity.upcCode
            upcCaseTv.text = itemEntity.upcCodeCase
            upcTv.setVisibleOrGone(!itemEntity.upcCode.isNullOrEmpty())
            labelUpcTv.setVisibleOrGone(!itemEntity.upcCode.isNullOrEmpty())
            upcCaseTv.setVisibleOrGone(!itemEntity.upcCodeCase.isNullOrEmpty())
            labelUpcCaseTv.setVisibleOrGone(!itemEntity.upcCodeCase.isNullOrEmpty())
            totalQtyTv.text = totalQtyShow
        }
    }

    //same item
    private fun showItemInventory(inventoryEntities: List<InventoryEntity>?) {
        val list = mutableListOf<InventoryItemSummaryEntity>()
        val groupedByLocation = inventoryEntities?.groupBy { v -> v.locationId }
        groupedByLocation?.forEach { (locationId, locationInventory) ->
            val groupedByLpId = locationInventory.groupBy { v -> v.lpId }
            val inventoryItemSummaryEntity = InventoryItemSummaryEntity().apply {
                this.locationId = locationId
                this.locationName = locationInventory[0].locationName
                this.lpCount = groupedByLpId.size
                this.lpIds = groupedByLpId.keys.toList()
                this.inventories = locationInventory
                this.totalQtyShow = generateShowTotalQty(locationInventory)
            }
            list.add(inventoryItemSummaryEntity)
        }
        itemAdapter.setDiffList(list)
    }

    //same lp
    private fun showLpInventory(inventoryEntities: List<InventoryEntity>?) {
        val list = mutableListOf<InventoryLpLocationSummaryEntity>()
        val groupedByLocation = inventoryEntities?.groupBy { v -> v.locationId }
        groupedByLocation?.forEach { (locationId, locationInventory) ->

            val groupedByItem = locationInventory.groupBy { v -> "${v.itemId}${v.lotNo ?: ""}" }
            groupedByItem.forEach { (_, itemInventory) ->
                val inventoryLpLocationSummaryEntity = InventoryLpLocationSummaryEntity().apply {
                    this.locationId = locationId
                    this.locationName = itemInventory[0].locationName
                    this.lpId = itemInventory[0].lpId
                    this.itemId = itemInventory[0].itemId
                    this.itemName = itemInventory[0].itemName
                    this.lotNo = itemInventory[0].lotNo
                    this.totalQtyShow = generateShowTotalQty(itemInventory)
                }
                list.add(inventoryLpLocationSummaryEntity)
            }
        }
        lpAdapter.setDiffList(list)
    }

    //same location
    private fun showLocationInventory(inventoryEntities: List<InventoryEntity>?) {
        val list = mutableListOf<InventoryLpLocationSummaryEntity>()
        val groupedByLpId = inventoryEntities?.groupBy { v -> v.lpId }
        groupedByLpId?.forEach { (lpId, lpInventory) ->

            val groupedByItem = lpInventory.groupBy { v -> "${v.itemId}${v.lotNo ?: ""}" }
            groupedByItem.forEach { (_, itemInventory) ->
                val inventoryLpLocationSummaryEntity = InventoryLpLocationSummaryEntity().apply {
                    this.locationId = itemInventory[0].locationId
                    this.locationName = itemInventory[0].locationName
                    this.lpId = lpId
                    this.itemId = itemInventory[0].itemId
                    this.itemName = itemInventory[0].itemName
                    this.lotNo = itemInventory[0].lotNo
                    this.totalQtyShow = generateShowTotalQty(itemInventory)
                }
                list.add(inventoryLpLocationSummaryEntity)
            }
        }
        locationAdapter.setDiffList(list)
    }
}