package com.unis.wms.task_action

import com.unis.platform.item.api.ItemApi
import com.unis.platform.item.model.ItemQueryEntity
import com.unis.platform.location_override.LocationOverrideHistoryApiService
import com.unis.platform.location_override.model.LocationOverrideHistoryCreateEntity
import com.unis.platform.location_v2.LocationApiService
import com.unis.platform.location_v2.model.LocationRequestEntity
import com.unis.platform.lp_v2.LpApiService
import com.unis.platform.lp_v2.model.LpLabelRequest
import com.unis.platform.lp_v2.model.LpRequestEntity
import com.unis.platform.task_action.TaskActionApi
import com.unis.platform.task_action.model.PickActionReportIssueEntity
import com.unis.platform.task_action.model.PickActionSuggestRequest
import com.unis.platform.task_action.model.PickStagePlaceActionEntity
import com.unis.platform.wcs.WcsLocalApiService
import com.unis.platform.wcs.WcsLocalManager
import com.unis.platform.wcs.model.WcsActionJobEntity
import com.unis.platform.wcs.model.WcsTaskActionRequest
import com.unis.platform.wcs.model.WcsActionSubmitEntity
import com.unis.platform.wcs.model.WcsActionSubmitResultEntity
import com.unis.platform.wcs.model.WcsTaskActionQueryEntity
import com.unis.platform.common.model.PageResponseEntity
import com.unis.platform.wcs.model.WcsTaskActionEntity
import com.unis.reactivemvi.mvvm.kotlin.BaseRepository
import com.unis.reactivemvi.mvvm.kotlin.extensions.apiServiceLazy
import kotlinx.coroutines.flow.Flow

class TaskActionRepository : BaseRepository() {

    private var wcsApiService: WcsLocalApiService? = null
    private val taskActionApi by apiServiceLazy<TaskActionApi>()
    private val lpApiService by apiServiceLazy<LpApiService>()
    private val itemApiService by apiServiceLazy<ItemApi>()
    private val locationApiService by apiServiceLazy<LocationApiService>()
    private val locationOverrideHistoryApiService by apiServiceLazy<LocationOverrideHistoryApiService>()

    init {
        val wcsServer = WcsLocalManager.getWcsServer()
        if (!wcsServer?.protocol.isNullOrEmpty() && !wcsServer?.ip.isNullOrEmpty()) {
            wcsApiService = WcsLocalApiService.createService(wcsServer?.protocol, wcsServer?.ip, wcsServer?.port)
        }
    }

    fun getTaskAction(request: WcsTaskActionRequest): Flow<WcsTaskActionEntity?>? {
        if (wcsApiService == null) return null
        return requestV2({wcsApiService!!.getTaskAction(request)})
    }

    fun submitTaskActionResult(cmdId: String, request: WcsActionSubmitEntity): Flow<WcsActionSubmitResultEntity?>? {
        if (wcsApiService == null) return null
        return requestV2({wcsApiService!!.submitTaskActionResult(cmdId, request)})
    }

    fun getPickActionSuggest(actionId: String, request: PickActionSuggestRequest)
    = requestV2({ taskActionApi.getPickActionSuggest(actionId, request) })

    fun createPickStagePlaceActions(request: PickStagePlaceActionEntity)
    = requestV2({ taskActionApi.createPickStagePlaceActions(request) })

    fun createLp(lpEntity: LpRequestEntity) = requestV2({ lpApiService.createLp(lpEntity) })

    fun createLpLabel(lpIds: List<String>, customerId: String?) = requestV2({
        lpApiService.buildLpLabel(LpLabelRequest().apply {
            this.lpIds = lpIds
            this.customerId = customerId
        })
    })

    fun searchLocationByName(name: String)= rxRequest2(locationApiService.searchLocations(LocationRequestEntity().apply {
        this.name = name
    }))

    fun searchItem(itemQueryEntity: ItemQueryEntity) = rxRequest2(itemApiService.searchItem(itemQueryEntity))

    fun searchLpsByCode(code: String) = requestV2({lpApiService.searchLps(LpRequestEntity().apply { this.code = code }) })

    fun reportIssue(request: PickActionReportIssueEntity)
    = requestV2({ taskActionApi.reportIssue(request) })

    /**
     * 跳过当前任务动作
     */
    fun skipTaskAction(actionId: String) = requestV2 (
        { taskActionApi.skipTaskAction(actionId) }
    )

    /**
     * 分页查询任务操作列表
     */
    fun searchTaskActionsByPaging(query: WcsTaskActionQueryEntity): Flow<PageResponseEntity<WcsActionJobEntity>?>? {
        if (wcsApiService == null) return null
        return requestV2({ wcsApiService!!.searchByPaging(query) })
    }

    /**
     * 获取建议的下一个位置
     */
    fun getSuggestedNextLocation(actionId: String) = requestV2({ taskActionApi.suggestNextLocation(actionId) })

    /**
     * 报告位置覆盖历史
     */
    fun reportLocationOverrideHistory(overrideHistoryCreate: LocationOverrideHistoryCreateEntity) = requestV2({
        locationOverrideHistoryApiService.create(overrideHistoryCreate)
    })
}

