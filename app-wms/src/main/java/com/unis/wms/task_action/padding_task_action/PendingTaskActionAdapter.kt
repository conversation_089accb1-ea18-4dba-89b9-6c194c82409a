package com.unis.wms.task_action.padding_task_action

import android.annotation.SuppressLint
import android.view.View
import com.linc.platform.utils.TimeUtil
import com.unis.platform.common.model.task.TaskType
import com.unis.platform.common.model.task.getDisplayName
import com.unis.platform.util.UomUtils
import com.unis.platform.wcs.model.WcsActionType
import com.unis.platform.wcs.model.WcsActionJobEntity
import com.unis.reactivemvi.mvvm.kotlin.BaseBindingDifferQuickAdapter
import com.unis.reactivemvi.mvvm.kotlin.BaseBindingViewHolder
import com.unis.wms.R
import com.unis.wms.databinding.ItemPendingTaskActionBinding
import java.util.*

class PendingTaskActionAdapter : BaseBindingDifferQuickAdapter<WcsActionJobEntity, ItemPendingTaskActionBinding>() {

    @SuppressLint("SetTextI18n")
    override fun convert(helper: BaseBindingViewHolder<ItemPendingTaskActionBinding>?, item: WcsActionJobEntity?) {
        helper?.binding?.apply {
            item?.let {
                val context = helper.itemView.context
                
                // 设置Action ID和状态
                actionIdTv.text = it.businessActionId ?: ""
                statusTv.text = it.status?.displayName() ?: ""
                
                // 设置优先级
                priorityTv.text = "${context.getString(R.string.label_priority)} ${it.priority?.toString() ?: ""}"

                // 设置操作类型
                actionTypeTv.text = getActionTypeText(it.actionType)
                
                // 设置任务ID和类型
                taskIdTv.text = it.businessId ?: ""
                taskTypeTv.text = getTaskTypeText(it.wmsTaskType)
                
                // 设置能力
                capabilitiesTv.text = it.requiredCapabilities?.joinToString(",") ?: ""
                
                // 根据actionType设置和控制新增字段的显示
                configureExtraFields(item, this)
                
                // 设置时间信息
                startTimeTv.text = formatTime(it.startTime)
                endTimeTv.text = formatTime(it.endTime)

                // 点击整行处理
                root.setOnClickListener {
                    onItemClickListener?.onItemClick(this@PendingTaskActionAdapter, it, helper.adapterPosition)
                }
            }
        }
    }
    
    /**
     * 根据操作类型配置附加字段的显示与隐藏
     */
    @SuppressLint("SetTextI18n")
    private fun configureExtraFields(item: WcsActionJobEntity, binding: ItemPendingTaskActionBinding) {
        when (item.actionType) {
            WcsActionType.PICK -> {
                // PICK操作展示：Item、Qty+UOM、FromLocation
                
                // 设置Item
                binding.itemLabel.visibility = View.VISIBLE
                binding.itemTv.visibility = View.VISIBLE
                binding.itemTv.text = item.extensionJson?.itemDisplayName ?: item.itemName ?: ""
                
                // 设置Qty+UOM
                binding.qtyUomLabel.visibility = View.VISIBLE
                binding.qtyUomTv.visibility = View.VISIBLE
                binding.qtyUomTv.text = "${item.qty ?: ""} ${UomUtils.extractUomName(item.uomId ?: "")}"
                
                // 设置FromLocation
                binding.fromLocationLabel.visibility = View.VISIBLE
                binding.fromLocationTv.visibility = View.VISIBLE
                binding.fromLocationTv.text = item.fromLocationName ?: ""
                
                // 隐藏不需要的字段
                binding.fromLpLabel.visibility = View.GONE
                binding.fromLpTv.visibility = View.GONE
                binding.toLocationLabel.visibility = View.GONE
                binding.toLocationTv.visibility = View.GONE
            }
            WcsActionType.PLACE -> {
                // PLACE操作展示：FromLocation、FromLP(s)、ToLocation
                
                // 设置FromLocation
                binding.fromLocationLabel.visibility = View.VISIBLE
                binding.fromLocationTv.visibility = View.VISIBLE
                binding.fromLocationTv.text = item.fromLocationName ?: ""
                
                // 设置FromLP(s)
                binding.fromLpLabel.visibility = View.VISIBLE
                binding.fromLpTv.visibility = View.VISIBLE
                // 如果有fromLpIds列表优先使用，否则使用fromLpId
                binding.fromLpTv.text = if (!item.fromLPIds.isNullOrEmpty()) {
                    item.fromLPIds!!.joinToString(", ")
                } else {
                    ""
                }
                
                // 设置ToLocation
                binding.toLocationLabel.visibility = View.VISIBLE
                binding.toLocationTv.visibility = View.VISIBLE
                binding.toLocationTv.text = item.toLocationName ?: ""
                
                // 隐藏不需要的字段
                binding.itemLabel.visibility = View.GONE
                binding.itemTv.visibility = View.GONE
                binding.qtyUomLabel.visibility = View.GONE
                binding.qtyUomTv.visibility = View.GONE
            }
            else -> {
                // 默认情况下隐藏所有附加字段
                binding.itemLabel.visibility = View.GONE
                binding.itemTv.visibility = View.GONE
                binding.qtyUomLabel.visibility = View.GONE
                binding.qtyUomTv.visibility = View.GONE
                binding.fromLocationLabel.visibility = View.GONE
                binding.fromLocationTv.visibility = View.GONE
                binding.fromLpLabel.visibility = View.GONE
                binding.fromLpTv.visibility = View.GONE
                binding.toLocationLabel.visibility = View.GONE
                binding.toLocationTv.visibility = View.GONE
            }
        }
    }
    
    private fun getActionTypeText(actionType: WcsActionType?): String {
        return when(actionType) {
            WcsActionType.PICK -> "PICK"
            WcsActionType.PLACE -> "PLACE"
            WcsActionType.AFFIX_LABEL -> "AFFIX LABEL"
            else -> "UNKNOWN"
        }
    }
    
    private fun getTaskTypeText(taskType: TaskType?): String {
        return taskType.getDisplayName()
    }
    
    private fun formatTime(time: Date?): String {
        return TimeUtil.formatDataString(TimeUtil.local2Default(time), TimeUtil.FORMAT_FULL_T_FORMAT, TimeUtil.FORMAT_FULL_FORMAT)

    }

    override fun areItemsTheSame(oldItem: WcsActionJobEntity, newItem: WcsActionJobEntity): Boolean {
        return oldItem.id == newItem.id
    }

    override fun areContentsTheSame(oldItem: WcsActionJobEntity, newItem: WcsActionJobEntity): Boolean {
        return oldItem.id == newItem.id &&
                oldItem.businessActionId == newItem.businessActionId &&
                oldItem.status == newItem.status &&
                oldItem.priority == newItem.priority &&
                oldItem.assigneeUserId == newItem.assigneeUserId &&
                oldItem.actionType == newItem.actionType &&
                oldItem.businessId == newItem.businessId &&
                oldItem.wmsTaskType == newItem.wmsTaskType &&
                oldItem.customerId == newItem.customerId &&
                oldItem.startTime == newItem.startTime &&
                oldItem.endTime == newItem.endTime
    }
} 