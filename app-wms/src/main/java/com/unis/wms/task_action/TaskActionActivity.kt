package com.unis.wms.task_action

import android.content.Context
import android.os.Bundle
import android.view.Menu
import android.view.MenuItem
import com.customer.widget.common.CallbackDialogV1
import com.customer.widget.extensions.setVisibleOrGone
import com.linc.platform.utils.ResUtil
import com.unis.platform.wcs.model.WcsActionType
import com.unis.reactivemvi.mvi.ReactiveViewScope
import com.unis.reactivemvi.mvi.SimpleReactiveActivity
import com.unis.reactivemvi.mvi.onEvent
import com.unis.reactivemvi.mvvm.kotlin.extensions.replaceFragment
import com.unis.wms.R
import com.unis.wms.databinding.ActivityTaskActionBinding
import com.unis.wms.task_action.padding_task_action.PendingTaskActionActivity
import com.unis.wms.task_action.pick.PickActionFragment
import com.unis.wms.task_action.place.PlaceActionFragment
import com.unis.wms.task_action.affix_label.AffixLabelActionFragment

class TaskActionActivity : SimpleReactiveActivity<TaskActionViewModel, TaskActionDataState, ActivityTaskActionBinding>() {

    companion object {
        private const val EXTRA_LOCATION = "extra_location"

        fun startActivity(context: Context, locationName: String?) {
            context.startActivity(
                android.content.Intent(context, TaskActionActivity::class.java).apply {
                    putExtra(EXTRA_LOCATION, locationName)
                }
            )
        }
    }

    override fun initView(savedInstanceState: Bundle?) {
        intent.getStringExtra(EXTRA_LOCATION)?.let {
            viewModel.getTaskAction(it)
            initToolBarWithSubTitle(binding.toolbar, getString(R.string.text_task_action), "")
        }
        showTaskActionFragment()
        binding.retryTv.setOnClickListener {
            viewModel.getTaskAction(intent.getStringExtra(EXTRA_LOCATION)?:"")
        }
    }

    override fun ReactiveViewScope.subscribeToUiState() {
    }

    override fun createViewModel(): TaskActionViewModel  = TaskActionViewModel()

    private fun showTaskActionFragment() = onEvent<TaskActionUiEvent.ShowWorkFragment> {
        when (wcsActionJob?.actionType) {
            WcsActionType.PLACE -> {
                binding.emptyTaskActionLl.setVisibleOrGone(false)
                initToolBarWithSubTitle(binding.toolbar, "${getString(R.string.title_place_action)} (${wcsActionJob.businessActionId})", "${wcsActionJob.businessId} (${wcsActionJob.wmsTaskType})")
                showPlaceActionFragment()
            }
            WcsActionType.PICK -> {
                binding.emptyTaskActionLl.setVisibleOrGone(false)
                initToolBarWithSubTitle(binding.toolbar, "${getString(R.string.title_pick_action)} (${wcsActionJob.businessActionId})", "${wcsActionJob.businessId} (${wcsActionJob.wmsTaskType})")
                showPickActionFragment()
            }
            WcsActionType.AFFIX_LABEL -> {
                binding.emptyTaskActionLl.setVisibleOrGone(false)
                initToolBarWithSubTitle(binding.toolbar, "${getString(R.string.title_affix_label_action)} (${wcsActionJob.businessActionId})", "${wcsActionJob.businessId} (${wcsActionJob.wmsTaskType})")
                showAffixLabelActionFragment()
            }
            else -> {
                binding.emptyTaskActionLl.setVisibleOrGone(!isLoading)
            }
        }
    }

    private fun showPlaceActionFragment() {
        replaceFragment(
            R.id.task_action_container_ll,
            PlaceActionFragment.newInstance(),
            tag = PlaceActionFragment.TAG
        )
    }

    private fun showPickActionFragment() {
        replaceFragment(
            R.id.task_action_container_ll,
            PickActionFragment.newInstance(),
            tag = PickActionFragment.TAG
        )
    }

    private fun showAffixLabelActionFragment() {
        replaceFragment(
            R.id.task_action_container_ll,
            AffixLabelActionFragment.newInstance(),
            tag = AffixLabelActionFragment.TAG
        )
    }

    override fun onCreateOptionsMenu(menu: Menu): Boolean {
        menuInflater.inflate(R.menu.menu_pick_action, menu)
        return true
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        return when (item.itemId) {
            R.id.menu_skip_action -> {
                viewModel.skipAction {
                    CallbackDialogV1.showConfirm(
                        context = this,
                        title = getString(R.string.text_skip_action),
                        message = ResUtil.format(R.string.msg_skip_action_check, viewModel.dataState.wcsActionJob?.actionType ?: "")
                    )
                }
                true
            }
            R.id.menu_user_pending_action -> {
                PendingTaskActionActivity.startActivity(this, viewModel.dataState.wcsActionJob?.businessActionId ?: "")
                true
            }
            else -> super.onOptionsItemSelected(item)
        }
    }
}


