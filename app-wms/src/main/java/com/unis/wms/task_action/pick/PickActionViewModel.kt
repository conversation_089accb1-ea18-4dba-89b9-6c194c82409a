package com.unis.wms.task_action.pick

import android.view.View
import com.customer.widget.common.safeCount
import com.linc.platform.print.commonprintlp.PrintData
import com.linc.platform.print.commonprintlp.PrintData.Companion.basic
import com.linc.platform.print.commonprintlp.PrintMsg
import com.linc.platform.print.model.LabelSizeEntry
import com.linc.platform.print.model.PrinterEntry
import com.linc.platform.utils.LPUtil
import com.linc.platform.utils.ResUtil
import com.unis.platform.item.model.ItemEntity
import com.unis.platform.item.model.ItemQueryEntity
import com.unis.platform.location_v2.model.LocationType
import com.unis.platform.lp_v2.model.LpRequestEntity
import com.unis.platform.lp_v2.model.LpType
import com.unis.platform.task_action.model.ActionStep
import com.unis.platform.task_action.model.ActionStepType
import com.unis.platform.task_action.model.ElementType
import com.unis.platform.task_action.model.PickActionSuggestRequest
import com.unis.platform.task_action.utils.TaskActionUtil
import com.unis.platform.util.UomUtils
import com.unis.platform.wcs.model.WcsActionResultDataEntity
import com.unis.platform.wcs.model.WcsActionResultEntity
import com.unis.platform.wcs.model.WcsActionSubmitEntity
import com.unis.reactivemvi.SnackType
import com.unis.reactivemvi.mvi.SimpleReactiveViewModel
import com.unis.reactivemvi.mvvm.kotlin.extensions.facilityName
import com.unis.reactivemvi.mvvm.kotlin.extensions.getFormattedString
import com.unis.reactivemvi.mvvm.kotlin.extensions.idmUserId
import com.unis.reactivemvi.mvvm.kotlin.extensions.launch
import com.unis.reactivemvi.mvvm.kotlin.extensions.showLoading
import com.unis.reactivemvi.mvvm.kotlin.extensions.showSnack
import com.unis.reactivemvi.mvvm.kotlin.extensions.showToast
import com.unis.reactivemvi.print.PrinterEvent
import com.unis.reactivemvi.print.PrinterManager
import com.unis.wms.R
import com.unis.wms.task_action.LpItem
import com.unis.wms.task_action.TaskActionViewModel
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.firstOrNull
import kotlinx.coroutines.flow.flowOf
import kotlinx.coroutines.isActive
import com.unis.platform.common.model.task.TaskType
import com.unis.platform.inventoryissue.InventoryIssueValues
import com.unis.platform.inventoryissue.model.InventoryIssueEntity
import com.unis.platform.task_action.model.PickActionReportIssueEntity

class PickActionViewModel(
    private val actionViewModel: TaskActionViewModel,
    val initialDataState: PickActionDataState = PickActionDataState()
) : SimpleReactiveViewModel<PickActionDataState>(initialDataState) {

    private val workViewMap: MutableMap<Int, View> = mutableMapOf()
    val wcsJobAction = actionViewModel.dataState.wcsActionJob
    private val curActionJobId = actionViewModel.dataState.curActionJobId
    val repository = actionViewModel.taskActionRepository

    init {
        getActionPage()
    }

    private fun getActionPage() {
        launch {
            wcsJobAction ?: return@launch
            if (wcsJobAction.businessId.isNullOrEmpty()) {
                showSnack(SnackType.ErrorV1(), R.string.error_task_action_not_found_wms_task_id)
                return@launch
            }

            val suggest = requestAwait(repository.getPickActionSuggest(wcsJobAction.businessActionId ?: "", PickActionSuggestRequest()))
                .getOrNull() ?: return@launch

            val action = if (suggest.locationType == LocationType.PICK) {
                TaskActionUtil.loadTaskActionFromAssets(ResUtil.getContext(), "pick_location_pick.json")
            } else {
                TaskActionUtil.loadTaskActionFromAssets(ResUtil.getContext(), "storage_location_pick.json")
            }

            // 调用suggestLocation方法
            action?.actionSteps?.forEach { step ->
                when (step.actionStepType) {
                    ActionStepType.SCAN_LOCATION -> {
                        updateTextElement(
                            step,
                            getFormattedString(R.string.suggest_location_hint, suggest.locationName ?: "")
                        )
                    }
                }
            }

            setDataStateAwait {
                copy(
                    wcsTaskAction = <EMAIL>,
                    taskUiAction = action,
                    pickActionSuggest = suggest,
                    curActionStep = if (suggest.locationType == LocationType.PICK) {
                        ActionStepType.SCAN_LOCATION
                    } else {
                        ActionStepType.SCAN_LP
                    }
                )
            }
            fireEvent { PickActionUiEvent.InitPickUiAction(taskUiAction = action) }
        }
    }

    private fun updateTextElement(step: ActionStep, value: String) {
        step.pageElements?.map {
            if (it.elements?.firstOrNull()?.type == ElementType.TEXT) {
                it.elements?.firstOrNull()?.value = value
            } else {
                it
            }
        }
    }

    fun putWorkViewMap(key: Int, view: View) {
        if (workViewMap.contains(key)) return
        workViewMap[key] = view
    }

    fun getWorkViewMap(): MutableMap<Int, View> {
        return workViewMap
    }

    fun getStepIndex(stepType: String): Int {
        return dataState.taskUiAction?.actionSteps?.indexOfFirst { it.actionStepType == stepType } ?: 0
    }

    fun getIndexByStep(index: Int): String {
        return dataState.taskUiAction?.actionSteps?.get(index)?.actionStepType ?: ActionStepType.SCAN_LOCATION
    }

    fun scanLocation(location: String) {
        if (location != dataState.pickActionSuggest?.locationName) {
            showSnack(SnackType.ErrorV1(), R.string.msg_not_allow_override_picking_suggested_location)
            return
        }
        updatePickWorkDataByPickLocation(locationName = location)
        fireEvent { PickActionUiEvent.UpdateWorkStep(getStepIndex(ActionStepType.SCAN_ITEM)) }
        setDataState { copy(curActionStep = ActionStepType.SCAN_ITEM) }
    }

    fun scanLp(scanLpId: String) {
        launch {
            awaitDataState()
            val suggestLps = dataState.pickActionSuggest?.lpItemSuggestions ?: emptyList()

            if (scanLpId !in suggestLps.mapNotNull { it.lpId }) {
                showSnack(SnackType.ErrorV1(), R.string.msg_lp_not_matched)
                return@launch
            }

            val suggestLpInfo = suggestLps.find { it.lpId == scanLpId } ?: return@launch
            val unPickedBaseQty = dataState.needPickBaseQty - dataState.pickedBaseQty

            // 当未拣货量为0时，提示不允许超拣物品
            if (unPickedBaseQty <= 0) {
                showSnack(SnackType.ErrorV1(), R.string.msg_not_allow_over_picked)
                return@launch
            }

            val scanLpPickedBaseQty = dataState.pickActionWorkDatas?.filter { it.fromLp == scanLpId }?.sumOf {
                UomUtils.calculateBaseQty(it.qty?.toDouble() ?: 0.0, it.uomId ?: "")
            }?:0.00
            val scanLpRemainBaseQty = suggestLpInfo.remainBaseQty?.minus(scanLpPickedBaseQty)?.toInt()?:0
            if (scanLpRemainBaseQty <= 0) {
                showSnack(SnackType.ErrorV1(), R.string.error_lp_already_not_item_able_pick)
                return@launch
            }

            val isCanEntreLp = scanLpRemainBaseQty <= unPickedBaseQty

            if (isCanEntreLp) {
                val entireActionWorkData = PickActionWorkData().apply {
                    location = dataState.pickActionSuggest?.locationName
                    this.fromLp = scanLpId
                    toLp = scanLpId
                    pickItemId = suggestLpInfo.itemId
                    pickItemName = wcsJobAction?.itemName
                    qty = scanLpRemainBaseQty
                    uomId = suggestLpInfo.uomId
                }
                val newPickActonWorkDatas = if (dataState.pickActionWorkDatas.isNullOrEmpty()) {
                    listOf(entireActionWorkData)
                } else {
                    dataState.pickActionWorkDatas?.toMutableList()?.apply {
                        add(0, entireActionWorkData)
                    }
                }
                setDataState { copy(pickActionWorkDatas = newPickActonWorkDatas) }
            } else {
                val expectQty = UomUtils.calculateTotalQty(unPickedBaseQty.toDouble(), suggestLpInfo.uomId ?: "").toInt()
                showEditQtyAndToLpDialog(expectQty, scanLpId)
            }
        }
    }

    fun updatePickWorkDataByScan(fromLpId: String, qty: Int, toLpId: String) {
        launch {
            awaitDataState()
            if (dataState.pickActionWorkDatas?.find { it.fromLp == fromLpId && it.toLp == toLpId } != null) {
                showSnack(SnackType.ErrorV1(), R.string.text_this_lp_already_in_list)
                return@launch
            }

            if (fromLpId == toLpId) {
                showSnack(SnackType.ErrorV1(), R.string.error_not_allow_entire_lp_pick)
                return@launch
            }

            val suggestLpInfo = dataState.pickActionSuggest?.lpItemSuggestions?.find { it.lpId == fromLpId }

            val entireActionWorkData = PickActionWorkData().apply {
                location = dataState.pickActionSuggest?.locationName
                this.fromLp = fromLpId
                toLp = toLpId
                pickItemId = suggestLpInfo?.itemId
                pickItemName = wcsJobAction?.itemName
                this.qty = qty
                uomId = suggestLpInfo?.uomId
            }
            val newPickActonWorkDatas = if (dataState.pickActionWorkDatas.isNullOrEmpty()) {
                listOf(entireActionWorkData)
            } else {
                dataState.pickActionWorkDatas?.toMutableList()?.apply {
                    add(0, entireActionWorkData)
                }
            }
            setDataState { copy(pickActionWorkDatas = newPickActonWorkDatas) }
        }
    }

    fun updateWorkDataByEdit(fromLpId: String, qty: Int, toLpId: String, originalToLpId: String) {
        launch {
            awaitDataState()
            if (dataState.pickActionWorkDatas?.find { it.fromLp == fromLpId && it.toLp == toLpId && qty == it.qty} != null) {
                showSnack(SnackType.ErrorV1(), R.string.text_this_lp_already_in_list)
                return@launch
            }

            if (fromLpId == toLpId) {
                showSnack(SnackType.ErrorV1(), R.string.error_not_allow_entire_lp_pick)
                return@launch
            }
            val pickedWorkData = dataState.pickActionWorkDatas?.find { it.fromLp == fromLpId && it.toLp == originalToLpId }
            val pickWorkData = PickActionWorkData().apply {
                location = pickedWorkData?.location
                this.fromLp = pickedWorkData?.fromLp
                toLp = toLpId
                pickItemId = pickedWorkData?.pickItemId
                pickItemName = pickedWorkData?.pickItemName
                this.qty = qty
                uomId = pickedWorkData?.uomId
            }

            val newPickActonWorkDatas = dataState.pickActionWorkDatas?.toMutableList()?.apply {
                removeIf { it.fromLp == fromLpId && it.toLp == originalToLpId }
                add(0, pickWorkData)
            }

            setDataState { copy(pickActionWorkDatas = newPickActonWorkDatas) }
        }
    }

    fun scanItem(keyword: String, onMultiItemsFounded: (List<ItemEntity>?) -> Flow<ItemEntity?>) {
        launch {
            val itemResult = requestAwait(repository.searchItem(ItemQueryEntity().apply {
                this.keyword = keyword
                this.customerId = wcsJobAction?.customerId
            })).getOrNull()
            val item = when (itemResult?.safeCount()) {
                0 -> {
                    showToast(R.string.msg_item_not_found)
                    null
                }

                1 -> itemResult.first()
                else -> onMultiItemsFounded(itemResult).firstOrNull()
            } ?: return@launch

            if (item.id != wcsJobAction?.itemId) {
                showSnack(SnackType.ErrorV1(), R.string.msg_item_not_matched)
                return@launch
            }
            updatePickWorkDataByPickLocation(itemId = item.id)

            fireEvent { PickActionUiEvent.UpdateWorkStep(getStepIndex(ActionStepType.INPUT_QTY)) }
            setDataState { copy(curActionStep = ActionStepType.INPUT_QTY) }
        }
    }

    fun inputQty(qty: String?) {
        val inputQty = qty?.toIntOrNull() ?: 0
        val actionQty = wcsJobAction?.qty ?: 0

        if (inputQty > actionQty) {
            showSnack(SnackType.ErrorV1(), R.string.error_qty_not_match)
            return
        }

        updatePickWorkDataByPickLocation(qty = inputQty)

        fireEvent { PickActionUiEvent.UpdateWorkStep(getStepIndex(ActionStepType.SCAN_TARGET_LP)) }
        setDataState { copy(curActionStep = ActionStepType.SCAN_TARGET_LP) }
    }

    fun scanTargetLp(lp: String) {
        val isLpMatch = LPUtil.isLP(lp)
        if (!isLpMatch) {
            showSnack(SnackType.ErrorV1(), getFormattedString(R.string.lp_not_found_by_xxx, lp))
            return
        }
        updatePickWorkDataByPickLocation(toLpId = lp)
        setDataState { copy(curActionStep = ActionStepType.SUBMIT) }
    }

    fun removeLp(lpItem: LpItem) {
        val newWorkDatas = dataState.pickActionWorkDatas?.toMutableList()
        newWorkDatas?.removeIf { it.fromLp == lpItem.lp && it.toLp == lpItem.toLp && it.qty == lpItem.qty }
        setDataState { copy(pickActionWorkDatas = newWorkDatas) }
    }

    fun updateWorkButtonVisibilityByIndex(index: Int) {
        setDataState { copy(curActionStep = getIndexByStep(index)) }
    }

    fun showEditQtyAndToLpDialog(expectQty: Int? = null, lpId: String? = null, toLpId: String?=null, isEdit: Boolean = false) {
        fireEvent { PickActionUiEvent.ShowEditQtyAndToLpDialog(expectQty ?: 0, lpId, toLpId, isEdit) }
    }

    fun checkQtyMath(qty: Int, fromLpId: String?, toLpId: String?, isEdit: Boolean, onSuccess: (Int, String) -> Unit) {
        val suggestLpItem = dataState.pickActionSuggest?.lpItemSuggestions?.find { it.lpId == fromLpId }
        val fromLpPickedBaseQty = if (isEdit) {
            dataState.pickActionWorkDatas?.filter { it.fromLp == fromLpId && it.toLp != toLpId}?.sumOf {
                UomUtils.calculateBaseQty(it.qty?.toDouble() ?: 0.0, it.uomId ?: "")
            }?:0.00
        } else {
            dataState.pickActionWorkDatas?.filter { it.fromLp == fromLpId }?.sumOf {
                UomUtils.calculateBaseQty(it.qty?.toDouble() ?: 0.0, it.uomId ?: "")
            }?:0.00
        }

        val scanLpRemainBaseQty = suggestLpItem?.remainBaseQty?.minus(fromLpPickedBaseQty)?.toInt()?:0
        val currentLpPickedBaseQty =  dataState.pickActionWorkDatas?.filter { it.fromLp == fromLpId && it.toLp == toLpId}?.sumOf {
            UomUtils.calculateBaseQty(it.qty?.toDouble() ?: 0.0, it.uomId ?: "")
        }?:0.00
        val unPickedBaseQty = if (isEdit) {
            (dataState.needPickBaseQty - dataState.pickedBaseQty) + currentLpPickedBaseQty.toInt()
        } else {
            dataState.needPickBaseQty - dataState.pickedBaseQty
        }

        // 当未拣货量为0时，提示不允许超拣物品
        if (qty > unPickedBaseQty) {
            showSnack(SnackType.ErrorV1(), R.string.msg_not_allow_over_picked)
            return
        }

        if (qty <= scanLpRemainBaseQty) {
            onSuccess(qty, fromLpId ?: "")
        } else {
            showSnack(SnackType.ErrorV1(), R.string.error_qty_not_match)
        }
    }

    fun checkScanTargetLp(lp: String, onSuccess: (String) -> Unit) {
        val isLpMatch = LPUtil.isLP(lp)
        if (!isLpMatch) {
            showSnack(SnackType.ErrorV1(), getFormattedString(R.string.lp_not_found_by_xxx, lp))
            return
        }
        onSuccess.invoke(lp)
    }

    private fun updatePickWorkDataByPickLocation(
        locationName: String? = null,
        qty: Int? = null,
        itemId: String? = null,
        toLpId: String? = null,
    ) {
        val pickWorkData = dataState.pickActionWorkDatas?.find { it.location == wcsJobAction?.fromLocationName }
        if (pickWorkData == null) {
            val entireActionWorkData = PickActionWorkData().apply {
                this.location = dataState.wcsTaskAction?.fromLocationName
                this.fromLp = dataState.pickActionSuggest?.lpItemSuggestions?.firstOrNull()?.lpId
                this.uomId = wcsJobAction?.uomId
                this.pickItemName = wcsJobAction?.itemName
            }
            setDataState { copy(pickActionWorkDatas = listOf(entireActionWorkData)) }
        } else {
            val newPickActonWorkDatas = dataState.pickActionWorkDatas?.toMutableList()
            val workData = newPickActonWorkDatas?.find { it.location == wcsJobAction?.fromLocationName } ?: return
            if (!locationName.isNullOrEmpty()) {
                workData.location = locationName
            }
            if (qty != null) {
                workData.qty = qty
            }
            if (!itemId.isNullOrEmpty()) {
                workData.pickItemId = itemId
            }
            if (!toLpId.isNullOrEmpty()) {
                workData.toLp = toLpId
            }
            setDataState { copy(pickActionWorkDatas = newPickActonWorkDatas) }
        }
    }

    fun printLp(onReprintConfirm: (message: String) -> Flow<Boolean?>, createLpSuccess: ((String) -> Unit)? = null) {
        launch {
            isActive
            // 准备打印数据
            val userId = repository.idmUserId
            val facilityName = repository.facilityName ?: ""
            val printData = basic(userId, facilityName, LabelSizeEntry.TWO_ONE)
            val printer = PrinterManager.getPrinter(userId, facilityName, LabelSizeEntry.TWO_ONE)

            if (printer == null) {
                // 如果没有打印机，通知用户设置打印机
                fireEvent { PickActionUiEvent.SetupPrinter }
                return@launch
            }
            val lpId = requestAwait(repository.createLp(LpRequestEntity().apply {
                type = LpType.CLP
                orderId = wcsJobAction?.orderId
                taskId = wcsJobAction?.businessId
            })).getOrNull() ?: return@launch
            val createLpZplCodeResult =
                requestAwait(repository.createLpLabel(listOf(lpId), wcsJobAction?.customerId)).getOrNull() ?: return@launch
            createLpSuccess?.invoke(lpId)
            // 设置打印数据
            printData.jobData = PrintData.JobData.ZPL(printCommands = createLpZplCodeResult.joinToString(""))

            // 执行打印
            executePrint(printer, printData, onReprintConfirm)

        }
    }

    /**
     * 执行打印操作
     */
    private suspend fun executePrint(
        printer: PrinterEntry,
        printData: PrintData,
        onReprintConfirm: (message: String) -> Flow<Boolean?> = { flowOf(false) }
    ) {
        PrinterManager.print(printData, printer, object : PrinterEvent() {
            override fun onShowProgress(show: Boolean) {
                showLoading(show)
            }

            override fun onError(errorMessage: String, printData: PrintData?, printer: PrinterEntry?) {
                showToast(errorMessage)
                if (printer != null && printData != null) {
                    handlePrintError(printer, errorMessage, printData, onReprintConfirm)
                }
            }

            override fun connectSuccess(printer: PrinterEntry) {}

            override fun printSuccess(printerData: PrintData, printer: PrinterEntry) {
                showToast(ResUtil.getString(R.string.msg_print_success))
            }
        })
    }

    /**
     * 处理打印错误
     */
    private fun handlePrintError(
        printer: PrinterEntry,
        errorMessage: String,
        printData: PrintData,
        onReprintConfirm: (message: String) -> Flow<Boolean?>
    ) {
        launch {
            val reprint = onReprintConfirm(PrintMsg.formatError(printer, errorMessage)).firstOrNull() ?: false
            if (reprint) {
                executePrint(printer, printData)
            }
        }
    }

    fun stage(onShowConfirmStageDialog: (List<PickActionWorkData>) -> Flow<Boolean?>) {
        launch {
            val mathPickActionWorkDatas = dataState.pickActionWorkDatas?.filter { it.isMatch }

            val confirmStage = if (!mathPickActionWorkDatas.isNullOrEmpty()) {
                onShowConfirmStageDialog(mathPickActionWorkDatas).firstOrNull()
            } else {
                true
            }

            val submit = if (mathPickActionWorkDatas.isNullOrEmpty()) {
                null
            } else {
                val submitDatas = mathPickActionWorkDatas.sortedBy { it.fromLp == it.toLp }.map {
                    WcsActionResultDataEntity().apply {
                        itemId = it.pickItemId
                        uomId = it.uomId
                        qty = it.qty?.toDouble()
                        fromLpId = it.fromLp
                        toLpId = it.toLp
                        orderId = wcsJobAction?.orderId
                    }
                }

                WcsActionSubmitEntity().apply {
                    action = wcsJobAction
                    jobId = curActionJobId
                    actionResult = WcsActionResultEntity().apply {
                        submitData = submitDatas
                    }
                }
            }

            if (confirmStage == true) {
                actionViewModel.stage(submit) {
                    clearDataAndGetAction(dataState.wcsTaskAction?.fromLocationName)
                }
            }
        }
    }

    /**
     * 显示库存问题对话框
     * 参照PickStepWorkViewModel实现
     */
    fun showInventoryIssueDialog(onShowConfirmReportDialog: () -> Flow<Boolean?>) {
        launch {
            val wcsTaskAction = actionViewModel.dataState.wcsActionJob
            val hasUncommittedData = !(dataState.pickActionWorkDatas?.filter { it.isMatch }.isNullOrEmpty())
            if (hasUncommittedData) {
                val confirmToReport = onShowConfirmReportDialog().firstOrNull() ?: false
                if (!confirmToReport) return@launch
            }

            val inventoryIssueEntity = InventoryIssueEntity().apply {
                this.taskId = wcsTaskAction?.businessId
                taskType = TaskType.PICK
                itemId = wcsTaskAction?.itemId
                lpId = dataState.pickActionWorkDatas?.firstOrNull()?.fromLp
                locationId = dataState.pickActionSuggest?.locationId
            }

            val issueList = InventoryIssueValues.getPickInventoryIssues(inventoryIssueEntity)
            fireEvent { PickActionUiEvent.ShowInventoryIssueDialog(issueList) }
        }
    }

    /**
     * 提交库存问题
     */
    fun submitInventoryIssue(inventoryIssue: InventoryIssueEntity?) {
        launch {
            if (inventoryIssue == null) return@launch
            //report issue
            val reportIssueResult = requestAwait(repository.reportIssue(PickActionReportIssueEntity().apply {
                this.issueType = inventoryIssue.issueType
                this.action = <EMAIL>
            }))

            if (!reportIssueResult.isSuccess) return@launch

            clearDataAndGetAction(dataState.wcsTaskAction?.fromLocationName)
        }
    }

    fun submit() {
        val wcsTaskAction = actionViewModel.dataState.wcsActionJob
        val mathPickActionWorkDatas = dataState.pickActionWorkDatas?.filter { it.isMatch }
        if (mathPickActionWorkDatas.isNullOrEmpty()) {
            showSnack(SnackType.ErrorV1(), R.string.error_no_data_to_submit)
            return
        }
        val submitDatas = mathPickActionWorkDatas.sortedBy { it.fromLp == it.toLp }.map {
            WcsActionResultDataEntity().apply {
                itemId = it.pickItemId
                uomId = it.uomId
                qty = it.qty?.toDouble()
                fromLpId = it.fromLp
                toLpId = it.toLp
                orderId = wcsTaskAction?.orderId
            }
        }
        val submitData = WcsActionSubmitEntity().apply {
            action = wcsTaskAction
            jobId = curActionJobId
            actionResult = WcsActionResultEntity().apply {
                submitData = submitDatas
            }
        }

        actionViewModel.submitTaskActionResult(submitData) {
            clearDataAndGetAction(submitData.action?.fromLocationName ?: "")
        }
    }

    private fun clearDataAndGetAction(currentLocation: String?) {
        setDataState { PickActionDataState.init() }
        actionViewModel.refreshTaskAction(currentLocation)
    }
}