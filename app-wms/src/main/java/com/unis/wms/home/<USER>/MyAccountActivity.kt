package com.unis.wms.home.profile

import android.content.Intent
import android.os.Build
import android.os.Bundle
import android.text.TextUtils
import com.linc.platform.core.LocalPersistence
import com.linc.platform.utils.ToastUtil
import com.unis.reactivemvi.mvi.ReactiveActivity
import com.unis.reactivemvi.mvi.ReactiveViewScope
import com.unis.wms.home.profile.dialog.MyAccountPswDialog
import android.view.inputmethod.EditorInfo
import android.widget.TextView.OnEditorActionListener
import com.customer.widget.GeneralAlertDialog
import com.customer.widget.VersionUpdateProgressDialog
import com.customer.widget.util.CommUtil
import com.unis.platform.util.SwitchLangUtils
import com.linc.platform.utils.UpdateManager
import com.unis.reactivemvi.mvi.onDeferredEvent
import com.unis.reactivemvi.mvi.onEvent
import com.unis.reactivemvi.mvvm.kotlin.androidx.lifecycle.runtime_ktx.lifecycleScope
import com.customer.widget.common.CallbackDialogV1
import com.customer.widget.util.GlobalConfig
import com.customer.widget.util.UiUtil
import com.unis.reactivemvi.common.collectDispatchToDeferred
import com.unis.wms.BuildConfig
import com.unis.wms.R
import com.unis.wms.databinding.ActivityMyAccountBinding


class MyAccountActivity : ReactiveActivity<MyAccountViewModel, MyAccountUiState, ActivityMyAccountBinding>() {

    private val versionUpdateProgressDialog by lazy { VersionUpdateProgressDialog(this) }

    override fun ReactiveViewScope.subscribeToUiState() {
        subscribe(MyAccountUiState::uom) {
            binding.sbUom.isChecked = it
        }
        subscribe(MyAccountUiState::inventory) {
            binding.sbInventory.isChecked = it
        }
        subscribe(MyAccountUiState::trace) {
            binding.sbTrace.isChecked = it
        }
    }

    override fun initView(savedInstanceState: Bundle?) {
        onEvent()
        initToolBar(binding.toolbar, R.string.setting_my_account)
        val languageEn = SwitchLangUtils.isLanguageEn()
        binding.apply {
            btnNewVersion.setOnClickListener {
                viewModel.checkUpdate(
                    Build.VERSION.SDK_INT < Build.VERSION_CODES.LOLLIPOP, BuildConfig.VERSION_CODE)
            }
            sbUom.setOnCheckedChangeListener { view, isChecked ->
                if (view.isPressed) {
                    return@setOnCheckedChangeListener
                }
                if (isChecked) {
                    MyAccountPswDialog.newInstance(selectType = 1, this@MyAccountActivity::checkPsw,this@MyAccountActivity::cancel)
                        .show(supportFragmentManager, MyAccountPswDialog::javaClass.name)
                } else {
                    LocalPersistence.setBoolean(this@MyAccountActivity, LocalPersistence.SP_KEY_REPLENISHMENT_UOM_DOWNGRADE, false)
                    viewModel.updateUom(false)
                }
            }

            sbInventory.setOnCheckedChangeListener { view, isChecked ->
                if (view.isPressed) {
                    return@setOnCheckedChangeListener
                }
                if (isChecked) {
                    MyAccountPswDialog.newInstance(selectType = 2, this@MyAccountActivity::checkPsw,this@MyAccountActivity::cancel)
                        .show(supportFragmentManager, MyAccountPswDialog::javaClass.name)
                } else {
                    LocalPersistence.setBoolean(
                        this@MyAccountActivity, LocalPersistence.SP_KEY_INVENTORY_ADJUSTMENT, false)
                    viewModel.updateInventory(false)
                }
            }
            sbTrace.setOnCheckedChangeListener { _, isChecked ->
                LocalPersistence.setBackEndTrackLog(this@MyAccountActivity, isChecked)
                viewModel.updateTrace(isChecked)
            }
//            tabLanguage.getTabAt(if (languageEn) 0 else 1)?.select()
//            tabLanguage.addOnTabSelectedListener(object : TabLayout.OnTabSelectedListener {
//                override fun onTabSelected(p0: TabLayout.Tab) {
//                    viewModel.updateLanguage(if (p0.position == 0) Locale.ENGLISH else Locale.CHINESE)
//                }
//
//                override fun onTabUnselected(p0: TabLayout.Tab) {
//                }
//
//                override fun onTabReselected(p0: TabLayout.Tab) {
//                }
//            })
            edtDevice.setText(LocalPersistence.getString(this@MyAccountActivity, LocalPersistence.SP_KEY_CUSTOMIZE_DEVICE_ID))
            edtDevice.setOnEditorActionListener(OnEditorActionListener { _, actionId, _ ->
                if (actionId == EditorInfo.IME_ACTION_DONE) { //点击搜索要做的操作
                    LocalPersistence.setString(
                        this@MyAccountActivity, LocalPersistence.SP_KEY_CUSTOMIZE_DEVICE_ID, edtDevice.text.toString())
                    CommUtil.hideKeyBoard(edtDevice)
                    edtDevice.clearFocus()
                    return@OnEditorActionListener true
                }
                false
            })
            tvDeviceType.text =
                if (UiUtil.isTablet(this@MyAccountActivity)) getString(R.string.text_is_tablet) else getString(R.string.text_is_not_tablet)
        }
    }

    override fun createViewModel() = MyAccountViewModel(
        initialState = MyAccountState(
            uom = LocalPersistence.getBoolean(
                this, LocalPersistence.SP_KEY_REPLENISHMENT_UOM_DOWNGRADE, false),
            inventory = LocalPersistence.getBoolean(
                this, LocalPersistence.SP_KEY_INVENTORY_ADJUSTMENT, false),
            trace = LocalPersistence.isNeedBackEndTrackLog(this),
            defaultMainServerUrl = BuildConfig.DEFAULT_MAIN_SERVER_URL)
    )


    private fun checkPsw(selectType: Int, psw: String) {
        val isEqual = TextUtils.equals(viewModel.dataState.password, psw)
        when (selectType) {
            1 -> {
                binding.sbUom.isChecked = isEqual
                viewModel.updateUom(isEqual)
                LocalPersistence.setBoolean(this@MyAccountActivity, LocalPersistence.SP_KEY_REPLENISHMENT_UOM_DOWNGRADE, isEqual)
            }
            2 -> {
                binding.sbInventory.isChecked = isEqual
                viewModel.updateInventory(isEqual)
                LocalPersistence.setBoolean(
                    this@MyAccountActivity, LocalPersistence.SP_KEY_INVENTORY_ADJUSTMENT, isEqual)
            }
        }
        if (!isEqual) {
            ToastUtil.showToast(R.string.error_incorrect_password)
        }
    }

    private fun cancel(selectType: Int) {
        when (selectType) {
            1 -> {
                binding.sbUom.isChecked = false
                viewModel.updateUom(false)
            }
            2->{
                binding.sbInventory.isChecked = false
                viewModel.updateInventory(false)
            }
        }
    }

    private fun onEvent() {
        onEvent<MyAccountUiEvent.ShowDownloadProgressDialog> {
            if (!versionUpdateProgressDialog.isShowing()) {
                versionUpdateProgressDialog.show(current, length)
            } else {
                versionUpdateProgressDialog.updateProgress(current, length)
            }

            if (isDone && versionUpdateProgressDialog.isShowing()) {
                versionUpdateProgressDialog.dismiss()
            }
        }

        onEvent<MyAccountUiEvent.LaunchInstall> {
            UpdateManager.installApkForResult(this@MyAccountActivity, fileName)
        }

        onEvent<MyAccountUiEvent.ShowUnSupportUpdateDialog> {
            GeneralAlertDialog.createAlertDialog(this@MyAccountActivity, getString(R.string.text_update_not_supported_api_under_21)).show()
        }

        onDeferredEvent<MyAccountUiEvent.ShowUpdateDialog, Boolean?> {
            CallbackDialogV1.showConfirm(
                this@MyAccountActivity,
                title = getString(R.string.title_alarm),
                message = getString(R.string.text_update),
                positiveText = getString(R.string.button_ok),
                negativeText = getString(R.string.btn_cancel)).collectDispatchToDeferred(lifecycleScope, it)
        }

    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (requestCode == UpdateManager.REQUEST_CODE_PACKAGE_INSTALL) {
            UpdateManager.installApk(this@MyAccountActivity, GlobalConfig.getUpdateFilePath(this@MyAccountActivity))
        }
    }
}