package com.unis.wms.home.profile

import com.unis.reactivemvi.mvi.DeferredUiEvent
import com.unis.reactivemvi.mvi.ReactiveDataState
import com.unis.reactivemvi.mvi.ReactiveUiState
import com.unis.reactivemvi.mvi.UiEvent

data class MyAccountState(
    val password: String = "",
    val uom: Boolean = false,
    val inventory: Boolean = false,
    val trace: Boolean = false,
    val defaultMainServerUrl: String = ""
) : ReactiveDataState {

}


data class MyAccountUiState(
    val uom: Boolean = false,
    val inventory: Boolean = false,
    val trace: Boolean = false
) : ReactiveUiState

interface MyAccountUiEvent {

    class ShowUpdateDialog : DeferredUiEvent<Boolean?>
    data class ShowDownloadProgressDialog(val current: Long, val length: Long, val isDone: Boolean) :
        UiEvent
    data class LaunchInstall(val fileName:String): UiEvent
    class ShowUnSupportUpdateDialog: UiEvent
}