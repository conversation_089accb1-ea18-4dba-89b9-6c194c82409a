package com.unis.wms.home.profile

import android.text.TextUtils
import com.customer.widget.util.GlobalConfig
import com.unis.platform.util.SwitchLangUtils
import com.linc.platform.home.UpdateApi
import com.linc.platform.home.VersionEntry
import com.linc.platform.http.DownloadInterceptor
import com.linc.platform.http.HttpService
import com.linc.platform.utils.Logger
import com.linc.platform.utils.ResUtil
import com.linc.platform.utils.UpdateManager
import com.unis.reactivemvi.mvi.ReactiveViewModel
import com.unis.reactivemvi.mvvm.kotlin.BaseRepository
import com.unis.reactivemvi.mvvm.kotlin.extensions.launch
import com.unis.reactivemvi.mvvm.kotlin.extensions.showToast
import com.unis.wms.R
import io.reactivex.rxjava3.android.schedulers.AndroidSchedulers
import io.reactivex.rxjava3.observers.DisposableObserver
import io.reactivex.rxjava3.schedulers.Schedulers
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import okhttp3.ResponseBody
import retrofit2.Response
import java.util.Locale

private const val FILE_NAME = "wms-debug.apk"

class MyAccountViewModel(
    private var repository: WorkStageRepo = WorkStageRepo(""),
    initialState: MyAccountState,
    initialUiState: MyAccountUiState = MyAccountUiState(),
) : ReactiveViewModel<MyAccountState, MyAccountUiState>(initialState, initialUiState) {

    private val updateDownloadApi = if (TextUtils.isEmpty(dataState.defaultMainServerUrl)) HttpService.createService(UpdateApi::class.java,
        DownloadInterceptor { current: Long, length: Long, isDone: Boolean ->
            updateProgressStep(current, length, isDone)
        }) else HttpService.createService(dataState.defaultMainServerUrl,
        UpdateApi::class.java,
        DownloadInterceptor { current: Long, length: Long, isDone: Boolean ->
            updateProgressStep(current, length, isDone)
        })

    init {
        repository = WorkStageRepo(dataState.defaultMainServerUrl)
        setUiState { copy(uom = dataState.uom, inventory = dataState.inventory, trace = dataState.trace) }
    }

    fun updateUom(isUom: Boolean) {
        launch {
            setUiStateAwait { copy(uom = isUom) }
            setDataStateAwait { copy(uom = isUom) }
        }
    }

    fun updateInventory(isInventory: Boolean) {
        launch {
            setUiStateAwait { copy(inventory = isInventory) }
            setDataStateAwait { copy(inventory = isInventory) }
        }
    }

    fun updateTrace(isTrace: Boolean) {
        setUiState { copy(trace = isTrace) }
        setDataState { copy(trace = isTrace) }
    }


    fun checkUpdate(supportUpdate: Boolean, versionCode: Int) {
        if (supportUpdate) {
            fireEvent { MyAccountUiEvent.ShowUnSupportUpdateDialog() }
            return
        }
        launch {
            requestAwait(repository.checkUpdate()).onSuccess {
                if (it != null && versionCode < it.versionCode) {
                    val confirm = awaitEvent { MyAccountUiEvent.ShowUpdateDialog() } ?: false
                    if (confirm) {
                        downLoadNewVersion(it)
                    }
                } else {
                    showToast(R.string.msg_in_latest_version)
                }
            }
        }
    }

    private fun downLoadNewVersion(versionEntry: VersionEntry) {
        var name = FILE_NAME
        if (!TextUtils.isEmpty(versionEntry.name)) {
            name = versionEntry.name
        }
        launch {
            updateDownloadApi.downloadFile(name).map { responseBody: Response<ResponseBody?> ->
                UpdateManager.writeFileToSD(GlobalConfig.getUpdateFilePath(ResUtil.getContext()), responseBody.body())
            }.subscribeOn(Schedulers.io()).observeOn(AndroidSchedulers.mainThread()).subscribe(object : DisposableObserver<Boolean?>() {
                override fun onComplete() {}
                override fun onError(e: Throwable) {
                    Logger.handleException(e)
                }

                override fun onNext(aBoolean: Boolean?) {
                    if (null != aBoolean && aBoolean) {
                        fireEvent { MyAccountUiEvent.LaunchInstall(fileName = GlobalConfig.getUpdateFilePath(ResUtil.getContext())) }
                    }
                }
            })
        }
    }

    private fun updateProgressStep(current: Long, length: Long, isDone: Boolean) {
        launch(context = Dispatchers.Main) {
            fireEvent { MyAccountUiEvent.ShowDownloadProgressDialog(current, length, isDone) }
        }
    }


    fun updateLanguage(locale: Locale){
        launch {
            delay(500)
            SwitchLangUtils.setLanguage(locale)
        }
    }

    class WorkStageRepo(defaultMainServerUrl: String) : BaseRepository() {
        private val updateApi = if (TextUtils.isEmpty(defaultMainServerUrl)) HttpService.createService(UpdateApi::class.java)
        else HttpService.createService(defaultMainServerUrl, UpdateApi::class.java, listOf())

        fun checkUpdate() = rxRequest(updateApi.loadVersion())
    }
}

