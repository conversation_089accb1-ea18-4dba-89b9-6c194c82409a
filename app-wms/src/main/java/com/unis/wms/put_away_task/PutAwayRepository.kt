package com.unis.wms.put_away_task

import com.unis.platform.common.api.BarcodeApi
import com.unis.platform.common.model.barcode.BarcodeTypeSearchEntity
import com.unis.platform.common.model.barcode.BarcodeTypeSearchEnum
import com.unis.platform.common.model.step.TaskStepApiService
import com.unis.platform.common.model.task.TaskQueryEntity
import com.unis.platform.common.model.task.TaskStartEntity
import com.unis.platform.customer_v2.CustomerApiService
import com.unis.platform.customer_v2.model.CustomerRequestEntity
import com.unis.platform.inventory.api.InventoryApi
import com.unis.platform.inventory.model.InventoryQueryEntity
import com.unis.platform.inventoryissue.model.InventoryIssueEntity
import com.unis.platform.item.api.ItemApi
import com.unis.platform.item.model.ItemQueryEntity
import com.unis.platform.location_override.LocationOverrideHistoryApiService
import com.unis.platform.location_override.model.LocationOverrideHistoryCreateEntity
import com.unis.platform.location_v2.LocationApiService
import com.unis.platform.location_v2.model.LocationRequestEntity
import com.unis.platform.location_v2.model.LocationStatus
import com.unis.platform.lp_v2.LpApiService
import com.unis.platform.lp_v2.model.LpLabelRequest
import com.unis.platform.lp_v2.model.LpRequestEntity
import com.unis.platform.put_away.PutAwayApiService
import com.unis.platform.put_away.model.LocationSuggestRequest
import com.unis.platform.put_away.model.PutAwayByItemSubmitEntity
import com.unis.platform.put_away.model.PutAwayLpDetailEntity
import com.unis.platform.put_away.model.PutAwaySearchEntity
import com.unis.platform.put_away.model.PutAwaySubmitEntity
import com.unis.platform.put_away.model.PutAwayTaskCreate
import com.unis.platform.put_away.model.PutAwayTaskUpdate
import com.unis.platform.put_away.model.PutAwayTypeEntity
import com.unis.platform.receipt.ReceiptApi
import com.unis.reactivemvi.mvvm.kotlin.BaseRepository
import com.unis.reactivemvi.mvvm.kotlin.extensions.apiServiceLazy

class PutAwayRepository : BaseRepository() {

    private val putAwayTaskApiService by apiServiceLazy<PutAwayApiService>()
    private val locationApiService by apiServiceLazy<LocationApiService>()
    private val taskStepApiService by apiServiceLazy<TaskStepApiService>()
    private val lpApiService by apiServiceLazy<LpApiService>()
    private val barcodeApi by apiServiceLazy<BarcodeApi>()
    private val inventoryApi by apiServiceLazy<InventoryApi>()
    private val itemApi by apiServiceLazy<ItemApi>()
    private val customApi by apiServiceLazy<CustomerApiService>()
    private val locationOverrideHistoryApiService by apiServiceLazy<LocationOverrideHistoryApiService>()
    private val receiptApi by apiServiceLazy<ReceiptApi>()

    fun createPutAwayTask(idmUserId: String, putAwayType: PutAwayTypeEntity, lpIds: List<String>? = null) = requestV2({
        putAwayTaskApiService.createPutAwayTask(PutAwayTaskCreate().apply {
            this.assigneeUserId = idmUserId
            this.putAwayType = putAwayType
            if (!lpIds.isNullOrEmpty()) {
                this.lpIds = lpIds
            }
        })
    })

    fun getPutAwayTask(taskId: String) = requestV2({ putAwayTaskApiService.getPutAwayTask(taskId) })

    fun updatePutAwayTask(update: PutAwayTaskUpdate) = requestV2({ putAwayTaskApiService.updatePutAwayTask(update) })

    fun searchPutAwayTasks(search: PutAwaySearchEntity) = requestV2({ putAwayTaskApiService.searchPutAwayTask(search) })

    fun searchPutAwayTasksByPaging(search: TaskQueryEntity) = requestV2({ putAwayTaskApiService.searchPutAwayTaskByPaging(search) })

    fun closeTask(taskId: String) = requestV2({ putAwayTaskApiService.closePutAwayTask(taskId) })

    fun loadPutAwaySuggestLps(taskId: String, request: PutAwayLpDetailEntity) = requestV2({
        putAwayTaskApiService.scanPutAwayLP(taskId, request)
    })

    fun starTaskStep(taskId: String) = requestV2({
        taskStepApiService.startPutAwayTaskAndStep(taskId, TaskStartEntity(forkliftId = null))})

    fun getSuggestLocation(lpIds: List<String>) = requestV2({
        putAwayTaskApiService.getSuggestLocation(LocationSuggestRequest().apply {
            this.lpIds = lpIds
        })
    })

    fun getLocation(locationName: String) = rxRequest2(locationApiService.searchLocations(LocationRequestEntity().apply {
        this.name = locationName
        this.status = LocationStatus.USABLE
    }))

    fun putAway(taskId: String, stepId: String, submitEntity: PutAwaySubmitEntity) = requestV2({
        putAwayTaskApiService.putAwayByLp(
            taskId, stepId, submitEntity
        )
    })

    fun getPutAwayHistory(taskId: String) = requestV2({ putAwayTaskApiService.getPutAwayHistory(taskId) })

    fun searchLpsByCode(code: String) = requestV2({ lpApiService.searchLps(LpRequestEntity().apply { this.code = code }) })

    fun searchBarcodeType(barcode: String) = requestV2({
        barcodeApi.searchBarcodeType(BarcodeTypeSearchEntity(barcode, listOf(BarcodeTypeSearchEnum.LP, BarcodeTypeSearchEnum.EQUIPMENT)))
    })

    fun searchInventories(query: InventoryQueryEntity) = rxRequest2(inventoryApi.searchInventory(query))

    fun searchItem(keyword: String, customerId: String?) =
        rxRequest2(itemApi.searchItem(ItemQueryEntity().apply {
            this.keyword = keyword
            this.customerId = customerId
        }))

    fun getLp(id: String) = requestV2({ lpApiService.getLp(id) })

    fun createLp(lpEntity: LpRequestEntity) = requestV2({ lpApiService.createLp(lpEntity) })

    fun createLpLabel(lpIds: List<String>, customerId: String?) = requestV2({
        lpApiService.buildLpLabel(LpLabelRequest().apply {
            this.lpIds = lpIds
            this.customerId = customerId
        })
    })

    fun putAwayByItem(taskId: String, stepId: String, submitEntity: PutAwayByItemSubmitEntity) =
        requestV2({ putAwayTaskApiService.putAwayByItem(taskId, stepId, submitEntity) })

    fun getSingleLpMultipleItemSuggestLocation(lpId: String, lastSuggestLocationId: String? = null, itemId: String? = null) =
        requestV2({
            putAwayTaskApiService.getSingleLpMultipleItemSuggestLocation(lpId, LocationSuggestRequest().apply {
                this.lastSuggestLocationId = lastSuggestLocationId
                this.suggestItemId = itemId
            })
        })

    fun getSuggestLocationForByLpPutAway(lpIds: List<String>, lastSuggestLocationId: String?) = requestV2({
        putAwayTaskApiService.getSuggestLocation(LocationSuggestRequest().apply {
            this.lpIds = lpIds
            this.lastSuggestLocationId = lastSuggestLocationId
        })
    })

    fun searchCustomers(customerIds: List<String>) = rxRequest2(customApi.searchCustomers(CustomerRequestEntity().apply {
        this.orgIds = customerIds
    }))

    fun validateLpCreateNewPutawayTask(lpId: String) = requestV2({
        putAwayTaskApiService.validateLpCreateNewPutawayTask(lpId)
    })

    fun reportLocationOverrideHistory(overrideHistoryCreate: LocationOverrideHistoryCreateEntity) = requestV2({
        locationOverrideHistoryApiService.create(overrideHistoryCreate)
    })
    
    fun getSubAllowDay(itemId: String) = requestV2({
        putAwayTaskApiService.getSubAllowDay(itemId)
    })
    
    fun getReceipt(receiptId: String) = requestV2({
        receiptApi.search(com.unis.platform.receipt.model.ReceiptQueryEntity().apply {
            this.receiptId = receiptId
        })
    })

}