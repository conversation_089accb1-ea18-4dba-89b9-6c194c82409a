package com.unis.wms.put_away_task.ui

import android.annotation.SuppressLint
import android.os.Bundle
import com.customer.widget.common.CenterDialog
import com.customer.widget.extensions.setVisibleOrGone
import com.linc.platform.utils.ResUtil
import com.unis.platform.location_override.model.LocationOverrideHistoryCreateEntity
import com.unis.platform.location_v2.model.LocationEntity
import com.unis.platform.location_v2.model.LocationType
import com.unis.reactivemvi.mvi.ReactiveFragment
import com.unis.reactivemvi.mvi.ReactiveViewScope
import com.unis.reactivemvi.mvi.onEvent
import com.unis.reactivemvi.mvvm.kotlin.extensions.getActivityViewModel
import com.unis.wms.R
import com.unis.wms.databinding.FragmentPutawayByLpWorkBinding
import com.unis.wms.location_override.LocationOverrideIssueReportDialog
import com.unis.wms.put_away_task.adapter.PutAwayScanLpAdapter
import com.unis.wms.put_away_task.model.PutAwayByLpWorkEvent
import com.unis.wms.put_away_task.model.PutAwayByLpWorkUiState
import com.unis.wms.put_away_task.model.ScanMode
import com.unis.wms.put_away_task.viewmodel.PutAwayViewModel
import com.unis.wms.put_away_task.viewmodel.PutAwayByLpWorkViewModel

class PutAwayByLpWorkFragment : ReactiveFragment<PutAwayByLpWorkViewModel, PutAwayByLpWorkUiState, FragmentPutawayByLpWorkBinding>() {

    companion object {
        const val TAG = "PutAwayByLpWorkFragment"
        fun newInstance() = PutAwayByLpWorkFragment()
        private const val MAX_VISIBLE_LOCATIONS = 2 // Maximum number of locations to show before "..." indicator
    }

    private val lpAdapter by lazy {
        PutAwayScanLpAdapter(onLPChecked = { viewModel.updateLpCheckedStatusWithLpVerify(it.lpId ?: "") })
    }
    
    private val activityViewModel by lazy { getActivityViewModel<PutAwayViewModel>() }

    override fun createViewModel(): PutAwayByLpWorkViewModel = PutAwayByLpWorkViewModel(activityViewModel)

    override fun initView(savedInstanceState: Bundle?) {
        binding?.apply {
            lpListRv.adapter = lpAdapter
            
            scanLpOrRnQs.setScanEvent { _, data ->
                viewModel.scanLpOrRn(data)
            }

            selectAllCb.setOnClickListener {
                viewModel.setSelectAllStatus(selectAllCb.isChecked)
            }

            scanModeSwitchBtn.setOnCheckedChangeListener { _, isChecked ->
                viewModel.setScanMode(isChecked)
            }

            suggestLocationBtn.setOnClickListener {
                viewModel.nextSuggestLocation()
            }

            scanLocationQs.setScanEvent { _, data ->
                viewModel.scanLocation(data)
            }

            putAwayBtn.setOnClickListener {
                viewModel.submit()
            }

            closeTaskBtn.setOnClickListener {
                activityViewModel?.closeTask()
            }
            
            suggestLocationsTv.setOnClickListener {
                viewModel.toggleSuggestLocationsExpanded()
            }
        }
        onShowLocationAvailableDialogEvent()
        onShowLPVerifyBeforePutAwayDialogEvent()
        onShowPartialPalletsNotAllowedDialogEvent()
        onShowLocationOverrideIssueDialog()
        onShowRnItemMismatchDialogEvent()
        onShowDateRangeExceededDialogEvent()
    }
    
    @SuppressLint("SetTextI18n")
    override fun ReactiveViewScope.subscribeToUiState() {
        subscribe(PutAwayByLpWorkUiState::scanLpTypes) {
            lpAdapter.setNewData(it)
//            binding?.selectAllCb?.setVisibleOrGone(it.isNotEmpty())
        }
        subscribe(PutAwayByLpWorkUiState::allLpCount, PutAwayByLpWorkUiState::selectedLpCount) { allLpCount, selectedLpCount ->
            binding?.selectedProcessTv?.text = " ($selectedLpCount/$allLpCount)"
        }
        subscribe(PutAwayByLpWorkUiState::isSelectAll) {
            binding?.selectAllCb?.isChecked = it
        }
        subscribe(PutAwayByLpWorkUiState::locationName) {
            binding?.scanLocationQs?.text = it
        }
        subscribe(PutAwayByLpWorkUiState::suggestLocationName) {
            binding?.suggestLocationBtn?.text =
                if (!it.isNullOrEmpty()) getString(R.string.text_next_location) else getString(R.string.get_suggestion)
            binding?.scanLocationQs?.setHintText(if (!it.isNullOrEmpty()) it else getString(R.string.hint_pick_v1_scan_location))
        }
        
        // Subscribe to suggested locations changes
        subscribe(PutAwayByLpWorkUiState::suggestLocations) { locations ->
            updateSuggestedLocationsUI(locations)
        }
        
        subscribe(PutAwayByLpWorkUiState::enableSubmit) {
            binding?.putAwayBtn?.isEnabled = it
        }
        subscribe(PutAwayByLpWorkUiState::scanMode) {
            binding?.scanModeTv?.text =
                if (it == ScanMode.SCAN) getString(R.string.label_scan_to_add) else getString(R.string.label_scan_to_subtract)
        }
        subscribe(PutAwayByLpWorkUiState::isSuggestLocationsExpanded) {
            binding?.suggestLocationsTv?.maxLines = if (it) Int.MAX_VALUE else 1
        }
        
        // 添加对currentRnItemName的订阅
        subscribe(PutAwayByLpWorkUiState::currentRnItemName) { itemName ->
            binding?.currentRnItemTv?.text = if (!itemName.isNullOrEmpty()) {
                getString(R.string.current_rn_item, itemName)
            } else {
                ""
            }
            binding?.currentRnItemTv?.setVisibleOrGone(!itemName.isNullOrEmpty())
        }
    }
    
    private fun updateSuggestedLocationsUI(locations: List<LocationEntity>) {
        binding?.apply {
            val hasLocations = locations.isNotEmpty()
            
            // Show/hide the suggested locations section
            suggestLocationsTitle.setVisibleOrGone(hasLocations)
            suggestLocationsTv.setVisibleOrGone(hasLocations)
            
            if (hasLocations) {
                // Convert locations to a comma-separated string
                val locationText = locations.joinToString(", ") { location ->
                    val countText = when (location.type) {
                        LocationType.LOCATION -> "(${location.lpCount} LPs)"
                        else -> "(${location.itemCount} Items)"
                    }
                    "${location.name} $countText"
                }
                suggestLocationsTv.text = locationText
                
                // Set click listener for each location
                suggestLocationsTv.setOnClickListener { view ->
                    viewModel.toggleSuggestLocationsExpanded()
                }

                // Update maxLines based on expanded state
                suggestLocationsTv.maxLines = if (viewModel.uiState.isSuggestLocationsExpanded) Int.MAX_VALUE else 1
            }
        }
    }

    private fun onShowLocationAvailableDialogEvent() = onEvent<PutAwayByLpWorkEvent.ShowLocationAvailableDialog> {
        val dialog = SelectLocationAvailabilityDialog.newInstance(location)
        dialog.show(childFragmentManager, SelectLocationAvailabilityDialog.TAG)
    }

    private fun onShowLPVerifyBeforePutAwayDialogEvent() = onEvent<PutAwayByLpWorkEvent.ShowLPVerifyBeforePutAwayDialog> {
        val dialog = LpVerifyDialog.newInstance(LpVerifyDialog.LpVerifyParam(receiptIds, lpInfo))
        dialog.setConfirmListener { receiptIds, putAwayLpInfo ->
            if (isFromScanOperation) {
                viewModel.handleScanLpTypes(receiptIds, listOf(putAwayLpInfo))
            } else {
                viewModel.updateLpCheckedStatusInState(putAwayLpInfo.lpId, putAwayLpInfo.isVerified)
            }
        }
        dialog.show(childFragmentManager, LpVerifyDialog.TAG)
    }

    private fun onShowPartialPalletsNotAllowedDialogEvent() = onEvent<PutAwayByLpWorkEvent.ShowPartialPalletsNotAllowedDialog> {
        context?: return@onEvent
        val message = String.format(ResUtil.getString(R.string.msg_partial_pallets_not_allowed), lpIds.joinToString(", "))
        CenterDialog.alert(
            context = context!!,
            message = message,
            okClick = {}
        ).show()
    }

    private fun onShowLocationOverrideIssueDialog() = onEvent<PutAwayByLpWorkEvent.ShowLocationOverrideIssueDialog> {
        LocationOverrideIssueReportDialog(context, issues, object : LocationOverrideIssueReportDialog.OnIssueItemClickListener {
            override fun onClick(overrideIssue: LocationOverrideHistoryCreateEntity?) {
                viewModel.handleAfterChooseOverrideIssue(specificLocation, overrideIssue)
            }

        }).build().show()
    }

    // 添加处理RN item不匹配事件的方法
    private fun onShowRnItemMismatchDialogEvent() = onEvent<PutAwayByLpWorkEvent.ShowRnItemMismatchDialog> {
        context ?: return@onEvent
        val message = String.format(
            ResUtil.getString(R.string.msg_rn_item_mismatch),
            scanningRnId,
            existingItemId,
            scanningItemId
        )
        CenterDialog.alert(
            context = context!!,
            message = message,
            okClick = {}
        ).show()
    }
    
    // 添加处理RN日期范围超限事件的方法
    private fun onShowDateRangeExceededDialogEvent() = onEvent<PutAwayByLpWorkEvent.ShowDateRangeExceededDialog> {
        context ?: return@onEvent
        val message = String.format(
            ResUtil.getString(R.string.msg_receipt_date_range_exceeded),
            receiptIds.joinToString(", "),
            maxAllowedDays,
            actualDays
        )
        CenterDialog.alert(
            context = context!!,
            message = message,
            okClick = {}
        ).show()
    }
}