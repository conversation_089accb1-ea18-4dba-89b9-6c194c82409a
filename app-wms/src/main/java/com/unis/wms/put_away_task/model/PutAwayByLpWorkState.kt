package com.unis.wms.put_away_task.model

import com.unis.platform.customer_v2.model.CustomerEntity
import com.unis.platform.location_override.model.LocationOverrideHistoryCreateEntity
import com.unis.platform.location_v2.model.LocationEntity
import com.unis.platform.put_away.model.PutAwayByItemSuggestionEntity
import com.unis.platform.put_away.model.PutAwayLpInfoEntity
import com.unis.reactivemvi.mvi.ReactiveDataState
import com.unis.reactivemvi.mvi.ReactiveUiState
import com.unis.reactivemvi.mvi.UiEvent

data class PutAwayByLpWorkDataState(
    val scanLpTypes: List<ScanLpType> = listOf(),
    val suggestLocationResultForFullPallet: PutAwayByItemSuggestionEntity? = null,
    val suggestLocationResultForPartialPallet: PutAwayByItemSuggestionEntity? = null,
    val suggestLocation: LocationEntity? = null,
    val lastSuggestLocationForPartialPallet: LocationEntity? = null,
    val lastSuggestLocationForFullPallet: LocationEntity? = null,
    val toLocation: LocationEntity? = null,
    val scanMode: ScanMode = ScanMode.SCAN,
    val customers: List<CustomerEntity> = listOf(),
    val currentRnItemId: String? = null,
) : ReactiveDataState {
    val allLpCount = scanLpTypes.count()
    val selectedLpCount = scanLpTypes.count { it.isCheck }
    val isSelectAll: Boolean = scanLpTypes.isNotEmpty() && scanLpTypes.all { it.isCheck }

    val selectedLpIds = scanLpTypes.filter { it.isCheck }.mapNotNull { it.lpId }
    
    val currentRnItemName: String? get() {
        return scanLpTypes.firstOrNull { 
            it.lpType == PutAwayLpType.Rn && it.lpInfoEntity?.itemId == currentRnItemId 
        }?.lpInfoEntity?.itemName
    }
}

data class PutAwayByLpWorkUiState(
    val scanLpTypes: List<ScanLpType> = listOf(),
    val allLpCount: Int = 0,
    val selectedLpCount: Int = 0,
    val isSelectAll: Boolean = false,
    val suggestLocationName: String? = null,
    val suggestLocations: List<LocationEntity> = emptyList(),
    val suggestLocationInfoMap: Map<String, String> = emptyMap(),
    val locationName: String? = null,
    val enableSubmit: Boolean = false,
    val scanMode: ScanMode = ScanMode.SCAN,
    val isSuggestLocationsExpanded: Boolean = false,
    val currentRnItemName: String? = null,
    val showRnItemWarning: Boolean = false
) : ReactiveUiState

interface PutAwayByLpWorkEvent {
    data class ShowLocationAvailableDialog(val location: LocationEntity): UiEvent
    data class ShowLPVerifyBeforePutAwayDialog(val receiptIds: List<String>?, val lpInfo: PutAwayLpInfoEntity, val isFromScanOperation: Boolean): UiEvent
    data class ShowPartialPalletsNotAllowedDialog(val lpIds: List<String>): UiEvent
    data class ShowLocationOverrideIssueDialog(val issues: List<LocationOverrideHistoryCreateEntity>, val specificLocation: LocationEntity?): UiEvent
    data class ShowRnItemMismatchDialog(val scanningRnId: String, val existingItemId: String, val scanningItemId: String): UiEvent
    data class ShowDateRangeExceededDialog(val receiptIds: List<String>, val maxAllowedDays: Int, val actualDays: Int): UiEvent
}

enum class ScanMode {
    SCAN, UN_SCAN
}
