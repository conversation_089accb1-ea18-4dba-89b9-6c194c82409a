package com.unis.wms.put_away_task.viewmodel

import android.text.TextUtils
import com.customer.widget.common.safeCount
import com.linc.platform.utils.LPUtil
import com.linc.platform.utils.ResUtil
import com.linc.platform.utils.StringUtil
import com.unis.platform.common.model.step.TaskStepType
import com.unis.platform.common.model.task.TaskType
import com.unis.platform.customer_v2.model.CustomerEntity
import com.unis.platform.inventoryissue.model.InventoryIssueEntity
import com.unis.platform.location_override.LocationOverrideHandle
import com.unis.platform.location_override.model.LocationOverrideHistoryCreateEntity
import com.unis.platform.location_v2.model.LocationEntity
import com.unis.platform.put_away.model.LpVerifyBeforePutAwayEntity
import com.unis.platform.put_away.model.PutAwayLpDetailEntity
import com.unis.platform.put_away.model.PutAwayLpInfoEntity
import com.unis.platform.put_away.model.PutAwaySubmitEntity
import com.unis.reactivemvi.SnackType
import com.unis.reactivemvi.mvi.ReactiveViewModel
import com.unis.reactivemvi.mvi.mapDataToUi
import com.unis.reactivemvi.mvi.subscribe
import com.unis.reactivemvi.mvvm.kotlin.extensions.launch
import com.unis.reactivemvi.mvvm.kotlin.extensions.showSnack
import com.unis.reactivemvi.mvvm.kotlin.extensions.showToast
import com.unis.reactivemvi.mvvm.kotlin.extensions.speak
import com.unis.wms.R
import com.unis.wms.put_away_task.PutAwayNextLocationHelper.getNexLocationFromSuggestLocationResult
import com.unis.wms.put_away_task.PutAwayRepository
import com.unis.wms.put_away_task.model.PutAwayByLpWorkDataState
import com.unis.wms.put_away_task.model.PutAwayByLpWorkEvent
import com.unis.wms.put_away_task.model.PutAwayByLpWorkUiState
import com.unis.wms.put_away_task.model.PutAwayLpType
import com.unis.wms.put_away_task.model.ScanLpType
import com.unis.wms.put_away_task.model.ScanMode
import com.unis.platform.put_away.model.PutAwayByItemSuggestionEntity
import com.unis.platform.put_away.model.ReceiptDateCheckEntity

class PutAwayByLpWorkViewModel(
    private val activityViewModel: PutAwayViewModel? = null,
    initialDataState: PutAwayByLpWorkDataState = PutAwayByLpWorkDataState(),
    initialUiState: PutAwayByLpWorkUiState = PutAwayByLpWorkUiState()
) : ReactiveViewModel<PutAwayByLpWorkDataState, PutAwayByLpWorkUiState>(initialDataState, initialUiState) {

    private val repository by lazy { PutAwayRepository() }
    private val taskId = activityViewModel?.dataState?.taskId

    init {
        mapDataToUi(PutAwayByLpWorkDataState::scanLpTypes, PutAwayByLpWorkUiState::scanLpTypes) { it }
        mapDataToUi(PutAwayByLpWorkDataState::allLpCount, PutAwayByLpWorkUiState::allLpCount) { it }
        mapDataToUi(PutAwayByLpWorkDataState::selectedLpCount, PutAwayByLpWorkUiState::selectedLpCount) { it }
        mapDataToUi(PutAwayByLpWorkDataState::scanMode, PutAwayByLpWorkUiState::scanMode) { it }
        mapDataToUi(PutAwayByLpWorkDataState::isSelectAll, PutAwayByLpWorkUiState::isSelectAll) { it }
        mapDataToUi(PutAwayByLpWorkDataState::toLocation, PutAwayByLpWorkUiState::locationName) { it?.name }
        mapDataToUi(PutAwayByLpWorkDataState::suggestLocation, PutAwayByLpWorkUiState::suggestLocationName) { it?.name }
        
        // 添加新的映射
        mapDataToUi(PutAwayByLpWorkDataState::currentRnItemName, PutAwayByLpWorkUiState::currentRnItemName) { it }
        
        // Map suggested locations to UI state
        mapDataToUi(
            PutAwayByLpWorkDataState::suggestLocationResultForFullPallet,
            PutAwayByLpWorkDataState::suggestLocationResultForPartialPallet,
            PutAwayByLpWorkUiState::suggestLocations
        ) { fullPalletResult, partialPalletResult ->
            val result = if (dataState.scanLpTypes.filter { it.isCheck }.all { it.lpInfoEntity?.isPartialPallet == true }) {
                partialPalletResult
            } else {
                fullPalletResult
            }
            
            val locations = result?.suggestLocations?.mapNotNull { suggestion ->
                suggestion.locationId?.let { result.locationMap?.get(it) }
            } ?: emptyList()
            
            // Reset expanded state when new locations are suggested
            if (locations.isNotEmpty()) {
                setUiState { copy(isSuggestLocationsExpanded = false) }
            }
            
            locations
        }
        
        mapDataToUi(
            PutAwayByLpWorkDataState::suggestLocationResultForFullPallet,
            PutAwayByLpWorkDataState::suggestLocationResultForPartialPallet,
            PutAwayByLpWorkUiState::suggestLocationInfoMap
        ) { fullPalletResult, partialPalletResult ->
            val result = if (dataState.scanLpTypes.filter { it.isCheck }.all { it.lpInfoEntity?.isPartialPallet == true }) {
                partialPalletResult
            } else {
                fullPalletResult
            }
            
            createLocationInfoMap(result)
        }
        
        mapDataToUi(
            PutAwayByLpWorkDataState::selectedLpCount,
            PutAwayByLpWorkDataState::toLocation,
            PutAwayByLpWorkUiState::enableSubmit
        ) { selectedLpCount, toLocation -> selectedLpCount > 0 && toLocation != null }

        subscribe(PutAwayByLpWorkDataState::selectedLpIds) {
            getNextSuggestLocation {}
        }

        val task = activityViewModel?.dataState?.task
        if (task?.receiveTaskId?.isNotEmpty() == true && task.receiptIds?.isNotEmpty() == true) {
            loadPutAwaySuggestLps(taskReceiptIds = task.receiptIds)
        }
    }

    fun scanLpOrRn(keyword: String) {
        if (taskId == null) return
        var tempKeyword = keyword
        launch {
            if (LPUtil.isRLP(tempKeyword)) {
                showSnack(SnackType.ErrorV1(), R.string.msg_for_rlp_please_use_by_item_method_to_put_away)
                return@launch
            }

            if (StringUtil.isNumeric(tempKeyword)) {
                val result = requestAwait(repository.searchLpsByCode(tempKeyword))
                if (result.isFailure) return@launch
                val lpEntities = result.getOrNull()
                val lp = when (lpEntities.safeCount()) {
                    0 -> {
                        showSnack(SnackType.ErrorV1(), R.string.msg_lp_not_found)
                        null
                    }

                    1 -> lpEntities!!.first()
                    else -> {
                        val lpIds = lpEntities!!.joinToString(separator = ",") { it.id }
                        showSnack(SnackType.ErrorV1(), String.format(ResUtil.getString(R.string.msg_multiple_lp_found), lpIds))
                        null
                    }
                } ?: return@launch
                tempKeyword = lp.id
            }

            if (!keyword.uppercase().startsWith("RN") && dataState.scanMode == ScanMode.UN_SCAN) {
                when {
                    dataState.scanLpTypes.any { it.lpId == tempKeyword } -> {
                        val newScanLpTypes = dataState.scanLpTypes.map { lpType ->
                            lpType.takeIf { it.lpId == tempKeyword }?.copy(isCheck = false) ?: lpType
                        }
                        setDataState { copy(scanLpTypes = newScanLpTypes) }
                    }

                    else -> showSnack(SnackType.ErrorV1(), R.string.msg_lp_was_not_collected_before, tempKeyword)
                }
                return@launch
            }

            loadPutAwaySuggestLps(keyword = keyword)
        }
    }

    private fun loadPutAwaySuggestLps(keyword: String? = null, taskReceiptIds: List<String>? = null) {
        taskId ?: return
        if (keyword == null && taskReceiptIds == null) return

        val receiptIds = taskReceiptIds ?: if (keyword?.uppercase()?.startsWith("RN") == true) listOf(keyword) else null

        val request = PutAwayLpDetailEntity().apply {
            if (!receiptIds.isNullOrEmpty()) {
                this.receiptIds = receiptIds
            } else {
                this.lpId = keyword
            }
        }

        request(repository.loadPutAwaySuggestLps(taskId, request)) {
            if (it.safeCount() == 1 && !TextUtils.isEmpty(it?.firstOrNull()?.customerId) && it?.first()?.isPartialPallet == true && !it.first().isVerified) {
                val lpInfo = it.first()
                checkLPVerifyBeforePutAway(lpInfo.customerId!!) { isPartialVerify ->
                    if (isPartialVerify) {
                        fireEvent { PutAwayByLpWorkEvent.ShowLPVerifyBeforePutAwayDialog(receiptIds, lpInfo, true) }
                    } else {
                        handleScanLpTypes(receiptIds, it)
                    }
                }
            } else {
                handleScanLpTypes(receiptIds, it)
            }
        }
    }

    fun handleScanLpTypes(
        receiptIds: List<String>?,
        lpInfoEntities: List<PutAwayLpInfoEntity>?
    ) {
        // 处理RN扫描
        if (!receiptIds.isNullOrEmpty()) {
            // 获取新扫描的RN对应的LP信息
            val newLpInfos = lpInfoEntities?.filter { lpInfo ->
                lpInfo.lpId !in dataState.scanLpTypes.map { scanLp -> scanLp.lpId }
            } ?: emptyList()
            
            // 检查是否存在item ID
            if (newLpInfos.isNotEmpty()) {
                // 获取新扫描RN的第一个item ID
                val newItemId = newLpInfos.firstOrNull()?.itemId
                
                // 如果当前已有RN并且有item ID，则需要检查一致性
                if (dataState.currentRnItemId != null && newItemId != null && dataState.currentRnItemId != newItemId) {
                    // 不同item ID的情况，触发警告事件
                    fireEvent { 
                        PutAwayByLpWorkEvent.ShowRnItemMismatchDialog(
                            receiptIds.firstOrNull() ?: "",
                            dataState.currentRnItemId ?: "",
                            newItemId
                        ) 
                    }
                    return // 不添加不匹配的RN
                }
                
                // 获取当前的itemId
                val itemId = if (dataState.currentRnItemId == null) newItemId else dataState.currentRnItemId
                
                // 如果有itemId，则校验日期范围
                if (itemId != null) {
                    launch {
                        // 验证收据日期范围
                        val isDateValid = validateReceiptDates(receiptIds, itemId)
                        
                        if (!isDateValid) {
                            return@launch // 日期校验不通过，不添加这些RN
                        }
                        
                        // 日期校验通过，继续处理
                        // 如果当前没有RN或者item ID一致，则更新当前item ID
                        val updatedItemId = if (dataState.currentRnItemId == null) newItemId else dataState.currentRnItemId
                        
                        // 创建并添加新的ScanLpType
                        val newLps = newLpInfos.map { lpInfo ->
                            ScanLpType(lpInfo.lpId, lpInfo, PutAwayLpType.Rn, false)
                        }
                        
                        // 更新状态
                        val updatedScanLpTypes = dataState.scanLpTypes + newLps
                        setDataState { 
                            copy(
                                scanLpTypes = updatedScanLpTypes,
                                currentRnItemId = updatedItemId
                            ) 
                        }
                    }
                    return
                }
                
                // 如果没有itemId，直接添加
                val updatedItemId = if (dataState.currentRnItemId == null) newItemId else dataState.currentRnItemId
                
                // 创建并添加新的ScanLpType
                val newLps = newLpInfos.map { lpInfo ->
                    ScanLpType(lpInfo.lpId, lpInfo, PutAwayLpType.Rn, false)
                }
                
                // 更新状态
                val updatedScanLpTypes = dataState.scanLpTypes + newLps
                setDataState { 
                    copy(
                        scanLpTypes = updatedScanLpTypes,
                        currentRnItemId = updatedItemId
                    ) 
                }
                return
            }
        }

        // 处理LP扫描
        // LP操作逻辑保持不变
        val newLpInfos = lpInfoEntities?.filter { lpInfo ->
            lpInfo.lpId !in dataState.scanLpTypes.map { scanLp -> scanLp.lpId }
        } ?: emptyList()

        if (newLpInfos.isEmpty()) return

        // 检查已有的LP类型（是否有Full/Partial混合的情况）
        if (dataState.scanLpTypes.isNotEmpty() && newLpInfos.isNotEmpty()) {
            // 获取已有LP的第一个LP的类型（Full or Partial）
            val existingLpType = dataState.scanLpTypes
                .firstOrNull { it.lpType == PutAwayLpType.Lp }
                ?.lpInfoEntity?.isPartialPallet

            // 检查所有新扫描的LP是否与已有LP的类型一致
            if (existingLpType != null) {
                val hasIncompatibleType = newLpInfos.any { lpInfo ->
                    lpInfo.isPartialPallet != existingLpType
                }
                
                if (hasIncompatibleType) {
                    // 显示错误消息：不允许混合Full和Partial类型的LP
                    showSnack(SnackType.ErrorV1(), R.string.msg_mixed_pallet_types_not_allowed)
                    return
                }
            }
        }

        val scanLpTypes = if (dataState.scanMode == ScanMode.SCAN) {
            dataState.scanLpTypes + newLpInfos.map { lpInfo ->
                ScanLpType(lpInfo.lpId, lpInfo, PutAwayLpType.Lp, true)
            }
        } else {
            // UN_SCAN mode, not supported for LP
            return
        }

        setDataState { copy(scanLpTypes = scanLpTypes) }
    }
    
    /**
     * 验证 Receipt 日期范围
     * 
     * 检查所有 RN 的接收日期是否在 sub-allow-day 允许的范围内
     * 
     * @param receiptIds 要校验的 receiptIds
     * @param itemId 物料ID，用于获取 sub-allow-day
     * @return 如果日期范围校验通过，返回 true；否则返回 false
     */
    private suspend fun validateReceiptDates(receiptIds: List<String>, itemId: String): Boolean {
        // 获取 sub-allow-day
        val subAllowDayResult = requestAwait(repository.getSubAllowDay(itemId))
        if (subAllowDayResult.isFailure) return true // 如果无法获取，默认通过验证
        
        val subAllowDay = subAllowDayResult.getOrNull() ?: return true
        
        // 获取所有 receipt 的日期信息
        val receiptsWithDates = mutableListOf<ReceiptDateCheckEntity>()
        
        // 添加当前已有的 RN 的日期信息
        val currentRnIds = dataState.scanLpTypes
            .filter { it.lpType == PutAwayLpType.Rn }
            .mapNotNull { it.lpId }
            .filter { it?.startsWith("RN", ignoreCase = true) == true }
        
        for (rnId in currentRnIds) {
            val receiptResult = requestAwait(repository.getReceipt(rnId))
            if (receiptResult.isSuccess) {
                val receiptList = receiptResult.getOrNull()
                receiptList?.firstOrNull()?.receivedTime?.let {
                    receiptsWithDates.add(ReceiptDateCheckEntity(rnId, it))
                }
            }
        }
        
        // 添加新扫描 RN 的日期信息
        for (receiptId in receiptIds) {
            val receiptResult = requestAwait(repository.getReceipt(receiptId))
            if (receiptResult.isSuccess) {
                val receiptList = receiptResult.getOrNull()
                receiptList?.firstOrNull()?.receivedTime?.let {
                    receiptsWithDates.add(ReceiptDateCheckEntity(receiptId, it))
                }
            }
        }
        
        // 如果只有一个 receipt 或没有 receipt，无需校验
        if (receiptsWithDates.size <= 1) return true
        
        // 计算最大和最小日期
        val dates = receiptsWithDates.map { it.receiveDate }
        val minDate = dates.minOrNull() ?: return true
        val maxDate = dates.maxOrNull() ?: return true
        
        // 计算日期跨度（天数差）
        val diffInMillis = maxDate.time - minDate.time
        val diffInDays = (diffInMillis / (1000 * 60 * 60 * 24)).toInt()
        
        // 检查是否超过限制
        if (diffInDays > subAllowDay) {
            fireEvent { PutAwayByLpWorkEvent.ShowDateRangeExceededDialog(receiptIds, subAllowDay, diffInDays) }
            return false
        }
        
        return true
    }

    fun setSelectAllStatus(isChecked: Boolean) {
        val newScanLpTypes = dataState.scanLpTypes.map { it.copy(isCheck = isChecked) }
        setDataState { copy(scanLpTypes = newScanLpTypes) }
    }

    fun setScanMode(checked: Boolean) {
        setDataState { copy(scanMode = if (checked) ScanMode.SCAN else ScanMode.UN_SCAN) }
    }

    fun updateLpCheckedStatusWithLpVerify(lpId: String) {
        val lpTypeIndex = dataState.scanLpTypes.indexOfFirst { it.lpId == lpId }
        if (lpTypeIndex < 0) return
        
        val lpType = dataState.scanLpTypes[lpTypeIndex]
        val isCurrentlyChecked = lpType.isCheck
        
        // If we're unchecking an LP, just update the state
        if (isCurrentlyChecked) {
            updateLpCheckedStatusInState(lpId)
            return
        }
        
        // If we're checking an LP, check if it's a partial pallet that needs verification
        val lpInfo = lpType.lpInfoEntity
        if (lpInfo != null && lpInfo.isPartialPallet && !lpInfo.isVerified) {
            // Check if customer requires verification
            lpInfo.customerId?.let { customerId ->
                checkLPVerifyBeforePutAway(customerId) { isPartialVerify ->
                    if (isPartialVerify) {
                        // Show verification dialog
                        fireEvent { PutAwayByLpWorkEvent.ShowLPVerifyBeforePutAwayDialog(null, lpInfo, false) }
                    } else {
                        // No verification needed, update state
                        updateLpCheckedStatusInState(lpId)
                    }
                }
            } ?: updateLpCheckedStatusInState(lpId) // No customer ID, just update state
        } else {
            // Not a partial pallet or already verified, update state
            updateLpCheckedStatusInState(lpId)
        }
    }
    
    // Helper method to update LP checked status in state
    internal fun updateLpCheckedStatusInState(lpId: String, isVerified: Boolean = false) {
        val newScanLpTypes = dataState.scanLpTypes.map { 
            if (it.lpId == lpId) it.copy(isCheck = !it.isCheck, lpInfoEntity = it.lpInfoEntity?.copy(isVerified = isVerified)) else it
        }
        setDataState { copy(scanLpTypes = newScanLpTypes) }
    }

    fun nextSuggestLocation() {
        val selectedLpIds = dataState.selectedLpIds
        if (selectedLpIds.isEmpty()) {
            showToast(R.string.please_scan_or_input_lp_to_suggest_location)
            return
        }
        if (hasMixedPartialPallets(selectedLpIds)) {
            fireEvent { PutAwayByLpWorkEvent.ShowPartialPalletsNotAllowedDialog(getPartialPalletLpIds(selectedLpIds)) }
            return
        }

        if (dataState.suggestLocation != null) {
            showOverrideLocationReportIssueDialog()
            return
        }

        getNextSuggestLocation {
            setDataState { copy(toLocation = null) }
        }
    }

    /**
     * Get the next suggested location
     * 
     * This method first checks if there are any selected LPs. If not, it clears the suggested location.
     * Then it tries to get the suggested location from cache. If the cache contains all selected items, it uses the cache directly.
     * Otherwise, it requests a new suggested location from the API.
     * 
     * @param onComplete Callback function to execute after getting the next suggested location
     */
    private fun getNextSuggestLocation(overrideIssue: LocationOverrideHistoryCreateEntity? = null, onComplete: () -> Unit = {}) {
        launch {
            if (dataState.selectedLpIds.isEmpty()){
                setDataState { copy(suggestLocation = null) }
                onComplete()
                return@launch
            }
            
            val selectedItemIds = dataState.scanLpTypes
                .filter { it.isCheck }
                .mapNotNull { it.lpInfoEntity?.itemId }
                .distinct()
            
            // When selecting LPs for suggestion, only homogeneous LP types are allowed
            // If all LPs are partial pallets, use suggestLocationResultForPartialPallet
            // If all LPs are full pallets, use suggestLocationResultForFullPallet
            // Mixed LP types (partial and full) are not allowed for suggestion
            val isAllPartialPallets = dataState.scanLpTypes
                .filter { it.isCheck }
                .all { it.lpInfoEntity?.isPartialPallet == true }
            
            // Determine last suggest location ID based on pallet type
            val lastSuggestLocationId = if (isAllPartialPallets) {
                dataState.lastSuggestLocationForPartialPallet?.id
            } else {
                dataState.lastSuggestLocationForFullPallet?.id
            }
            
            val cachedSuggestResult = if (isAllPartialPallets) {
                dataState.suggestLocationResultForPartialPallet
            } else {
                dataState.suggestLocationResultForFullPallet
            }
            
            if (cachedSuggestResult != null) {
                val cachedItemIds = cachedSuggestResult.suggestLocations?.mapNotNull { it.itemId } ?: emptyList()
                
                if (selectedItemIds.isNotEmpty() && selectedItemIds.all { it in cachedItemIds }) {
                    val nextLocation = getNexLocationFromSuggestLocationResult(lastSuggestLocationId, cachedSuggestResult)
                    
                    // Get all suggested locations
                    val allSuggestLocations = cachedSuggestResult.suggestLocations?.mapNotNull { suggestion ->
                        suggestion.locationId?.let { cachedSuggestResult.locationMap?.get(it) }
                    } ?: emptyList()
                    
                    // Create location info map with inventory details
                    val locationInfoMap = createLocationInfoMap(cachedSuggestResult)
                    
                    if (nextLocation != null) {
                        setDataState { 
                            if (isAllPartialPallets) {
                                copy(
                                    suggestLocation = nextLocation, 
                                    lastSuggestLocationForPartialPallet = nextLocation
                                )
                            } else {
                                copy(
                                    suggestLocation = nextLocation, 
                                    lastSuggestLocationForFullPallet = nextLocation
                                )
                            }
                        }
                        onComplete()
                        return@launch
                    }
                }
            }
            
            val result = requestAwait(repository.getSuggestLocationForByLpPutAway(dataState.selectedLpIds, lastSuggestLocationId))
            
            val suggestionEntity = result.getOrNull()
            val location = suggestionEntity?.suggestLocations?.firstOrNull()?.locationId?.let {
                suggestionEntity.locationMap?.get(it)
            }
            
            // Get all suggested locations
            val allSuggestLocations = suggestionEntity?.suggestLocations?.mapNotNull { suggestion ->
                suggestion.locationId?.let { suggestionEntity.locationMap?.get(it) }
            } ?: emptyList()
            
            // Create location info map with inventory details
            val locationInfoMap = createLocationInfoMap(suggestionEntity)
            
            if (isAllPartialPallets) {
                setDataState { 
                    copy(
                        suggestLocationResultForPartialPallet = suggestionEntity, 
                        suggestLocation = location,
                        lastSuggestLocationForPartialPallet = location
                    ) 
                }
            } else {
                setDataState { 
                    copy(
                        suggestLocationResultForFullPallet = suggestionEntity, 
                        suggestLocation = location,
                        lastSuggestLocationForFullPallet = location
                    ) 
                }
            }

            reportLocationOverrideHistory(location, overrideIssue)
            onComplete()
        }
    }

    /**
     * Creates a map of location ID to inventory information string
     */
    private fun createLocationInfoMap(suggestionEntity: PutAwayByItemSuggestionEntity?): Map<String, String> {
        val result = mutableMapOf<String, String>()
        
        suggestionEntity?.suggestLocations?.forEach { suggestion ->
            val locationId = suggestion.locationId ?: return@forEach
            val location = suggestionEntity.locationMap?.get(locationId) ?: return@forEach
            
            // Get lpCount and itemCount from the suggestion object, not from the location
            val palletCount = suggestion.lpCount ?: 0
            val itemCount = suggestion.itemCount ?: 0
            
            // Check location type and display appropriate count based on requirements
            val info = when {
                // For locations with type "LOCATION", display lpCount
                location.type.toString().equals("LOCATION", ignoreCase = true) -> {
                    if (palletCount > 0) "($palletCount LPs)" else ""
                }
                // For other location types, display itemCount
                else -> {
                    if (itemCount > 0) "($itemCount Items)" else ""
                }
            }
            
            result[locationId] = info
        }
        
        return result
    }

    fun scanLocation(locationName: String) {
        launch {
            val result = requestAwait(repository.getLocation(locationName))
            if (result.isFailure) return@launch
            val locations = result.getOrNull()
            if (locations.isNullOrEmpty()) {
                showSnack(SnackType.ErrorV1(), R.string.msg_invalid_location)
                speak(ResUtil.getString(R.string.message_invalid_location))
                return@launch
            }

            val location = locations.first()
            if (location.depletedFlag) {
                showToast(R.string.msg_not_allow_put_away_to_depleted_location)
                return@launch
            }

            val suggestLocation = dataState.suggestLocation
            if (suggestLocation != null && location.name != suggestLocation.name) {
                showOverrideLocationReportIssueDialog(location)
                return@launch
            }

            setDataState { copy(toLocation = location) }
        }
    }

    fun handleAfterChooseOverrideIssue(specificLocation: LocationEntity?, overrideIssue: LocationOverrideHistoryCreateEntity?) {
        launch {
            overrideIssue ?: return@launch
            if (specificLocation != null) {
                requestAwait(repository.reportLocationOverrideHistory(overrideIssue)).getOrNull() ?: return@launch
                setDataState { copy(toLocation = specificLocation) }
                return@launch
            }

            getNextSuggestLocation(overrideIssue = overrideIssue) {
                setDataState { copy(toLocation = null) }
            }
        }
    }

    private fun reportLocationOverrideHistory(specificLocation: LocationEntity?, overrideIssue: LocationOverrideHistoryCreateEntity?) {
        launch {
            overrideIssue ?: return@launch
            overrideIssue.overrideLocationId = specificLocation?.id
            requestAwait(repository.reportLocationOverrideHistory(overrideIssue), showLoading = false)
        }
    }

    private fun hasMixedPartialPallets(lpIds: List<String>?): Boolean {
        if (lpIds.isNullOrEmpty()) return false
        
        val isPartialPalletValues = dataState.scanLpTypes
            .filter { scanLpType -> scanLpType.lpId in lpIds }
            .mapNotNull { it.lpInfoEntity?.isPartialPallet?: false }
            .distinct()
        
        return isPartialPalletValues.size > 1
    }

    private fun getPartialPalletLpIds(lpIds: List<String>?): List<String> {
        if (lpIds.isNullOrEmpty()) return emptyList()
        
        return dataState.scanLpTypes
            .filter { scanLpType -> 
                scanLpType.lpId in lpIds && scanLpType.lpInfoEntity?.isPartialPallet == true 
            }
            .mapNotNull { it.lpId }
    }

    private fun checkLPVerifyBeforePutAway(customerId: String, action: (Boolean) -> Unit) {
        launch {
            requestAwait(repository.searchCustomers(listOf(customerId))).onSuccess {
                saveCustomers(it)
                action(isPartialVerifyBeforePutAway(it?.firstOrNull()))
            }
        }
    }   

    private fun checkConfirmCapacityAfterPutAway(customerIds: List<String>, action: (Boolean) -> Unit) {
        launch {
            requestAwait(repository.searchCustomers(customerIds)).onSuccess {
                saveCustomers(it)
                action(isConfirmCapacityAfterPutAway(it))
            }
        }
    }

    private fun isConfirmCapacityAfterPutAway(customers: List<CustomerEntity>?): Boolean {
        return customers?.any { customer -> isConfirmCapacityAfterPutAway(customer) } ?: false
    }

    private fun isConfirmCapacityAfterPutAway(customer: CustomerEntity?): Boolean {
        return customer?.inboundSetting?.confirmCapacityAfterPutAway == true
    }

    private fun isPartialVerifyBeforePutAway(customer: CustomerEntity?): Boolean {
        return customer?.inboundSetting?.lpVerifyBeforePutAway == LpVerifyBeforePutAwayEntity.PARTIAL
    }

    private fun saveCustomers(customers: List<CustomerEntity>?) {
        if (customers.isNullOrEmpty()) return
        
        val newCustomers = dataState.customers.toMutableList().apply {
            addAll(customers)
        }.distinctBy { it.orgId }
        
        setDataState { copy(customers = newCustomers) }
    }

    fun getCustomer(customerId: String): CustomerEntity? {
        return dataState.customers.firstOrNull { it.orgId == customerId }
    }

    fun getCustomers(customerIds: List<String>): List<CustomerEntity> {
        return dataState.customers.filter { it.orgId in customerIds }
    }

    
    /**
     * Checks if capacity confirmation is needed after put-away for the given LP IDs.
     * @param lpIds List of LP IDs to check
     * @param action Callback with boolean result indicating if confirmation is needed
     */
    private fun checkConfirmCapacityAfterPutAwayByLpIds(lpIds: List<String>, action: (Boolean) -> Unit) {
        // Get customerIds from scanLPTypes and remove duplicates
        val customerIds = dataState.scanLpTypes
            .filter { it.lpId in lpIds }
            .mapNotNull { it.lpInfoEntity?.customerId }
            .distinct()
        
        if (customerIds.isEmpty()) {
            action(false)
            return
        }
        
        // Get existing customers from the current state
        val existingCustomers = getCustomers(customerIds)
        
        if (existingCustomers.isEmpty()) {
            // If no customers found, use checkConfirmCapacityAfterPutAway logic
            checkConfirmCapacityAfterPutAway(customerIds, action)
            return
        }
        
        if (existingCustomers.size <= dataState.customers.size) {
            // If we have all needed customers in our existing list
            // Check directly if any need capacity confirmation
            action(isConfirmCapacityAfterPutAway(existingCustomers))
            return
        }
        
        // If we have some of the needed customers
        if (isConfirmCapacityAfterPutAway(existingCustomers)) {
            // If any existing customer needs confirmation, return true immediately
            action(true)
            return
        }
        
        // Find customerIds that are not in our existing customers list
        val missingCustomerIds = customerIds.filter { customerId ->
            existingCustomers.none { it.orgId == customerId }
        }
        
        if (missingCustomerIds.isEmpty()) {
            // If all customerIds are in existingCustomers and none need confirmation
            action(false)
            return
        }
        
        // Check the missing customerIds that aren't in our current state
        checkConfirmCapacityAfterPutAway(missingCustomerIds) { needConfirm ->
            action(needConfirm)
        }
    }

    fun toggleSuggestLocationsExpanded() {
        setUiState { copy(isSuggestLocationsExpanded = !isSuggestLocationsExpanded) }
    }

    fun submit() {
        val lpIds = dataState.scanLpTypes.filter { it.isCheck }.map { it.lpId ?: "" }
        if (lpIds.isEmpty()) {
            showToast(R.string.please_scan_or_input_lp_to_put_away)
            return
        }
        
        // Check for mixed partial pallets
        if (hasMixedPartialPallets(lpIds)) {
            fireEvent { PutAwayByLpWorkEvent.ShowPartialPalletsNotAllowedDialog(getPartialPalletLpIds(lpIds)) }
            return
        }
        
        val taskId = activityViewModel?.dataState?.taskId ?: return
        val stepId = activityViewModel.dataState.task?.taskSteps?.get(0)?.id ?: return
        val toLocation = dataState.toLocation
        val submitEntity = PutAwaySubmitEntity().apply {
            toLocationId = toLocation?.id
            this.lpIds = lpIds
        }
        request(repository.putAway(taskId, stepId, submitEntity)) {
            showSnack(SnackType.SuccessV1(), R.string.put_away_success)
            val newLpTypes = dataState.scanLpTypes.filter { !it.isCheck }
            setDataState { 
                copy(
                    scanLpTypes = newLpTypes, 
                    toLocation = null, 
                    suggestLocation = null,
                    currentRnItemId = null // 重置当前RN item ID
                ) 
            }
            toLocation?.let {
                checkConfirmCapacityAfterPutAwayByLpIds(lpIds) { needConfirm ->
                    if (needConfirm) {
                        fireEvent { PutAwayByLpWorkEvent.ShowLocationAvailableDialog(it) }
                    }
                }
            }
        }
    }

    private fun showOverrideLocationReportIssueDialog(specificLocation: LocationEntity? = null) {
        val taskId = activityViewModel?.dataState?.taskId ?: return
        val locationOverrideIssue = LocationOverrideHistoryCreateEntity().apply {
            this.taskId = taskId
            this.taskType = TaskType.PUT_AWAY
            this.taskStepType = TaskStepType.PUT_AWAY
            this.taskAssigneeUserId = activityViewModel.dataState.task?.assigneeUserId
            this.suggestedLocationId = dataState.suggestLocation?.id
            this.overrideLocationId = specificLocation?.id
        }
        fireEvent {
            PutAwayByLpWorkEvent.ShowLocationOverrideIssueDialog(
                LocationOverrideHandle.getPutAwayLocationOverrideIssues(locationOverrideIssue),
                specificLocation
            )
        }
    }
}