package com.unis.wms.replenishment.work.dropsubmit

import com.customer.widget.common.safeCount
import com.linc.platform.R
import com.linc.platform.utils.ToastUtil
import com.unis.platform.uom.model.UomEntity
import com.unis.platform.location_v2.LocationApiService
import com.unis.platform.location_v2.model.LocationEntity
import com.unis.platform.location_v2.model.LocationRequestEntity
import com.unis.platform.lp_v2.LpApiService
import com.unis.platform.lp_v2.model.LpRequestEntity
import com.unis.platform.replenishment.model.DropCreateEntity
import com.unis.platform.replenishment.api.ReplenishmentTaskApi
import com.unis.platform.uom.ItemUomApi
import com.unis.platform.uom.model.UomQueryEntity
import com.unis.reactivemvi.SnackType
import com.unis.reactivemvi.mvi.ReactiveViewModel
import com.unis.reactivemvi.mvi.mapDataToUi
import com.unis.reactivemvi.mvvm.kotlin.BaseRepository
import com.unis.reactivemvi.mvvm.kotlin.extensions.*
import com.unis.wms.replenishment.work.ReplenishmentTaskViewModel
import com.unis.wms.replenishment.work.ReplenishmentValidator
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.firstOrNull

class ReplenishmentDropSubmitViewModel(
    private val activityViewModel: ReplenishmentTaskViewModel,
    initialState: ReplenishmentDropSubmitDataState = ReplenishmentDropSubmitDataState(),
    initialUiState: ReplenishmentDropSubmitUiState = ReplenishmentDropSubmitUiState()
) : ReactiveViewModel<ReplenishmentDropSubmitDataState, ReplenishmentDropSubmitUiState>(initialState, initialUiState) {

    private val dropProcessDataEntity get() = activityViewModel.getDropProcessDataEntity()
    private val repository: Repository = Repository()

    init {
        autoUpdateDataToUi()
        setNeedPrintLp(false)
        if (activityViewModel.isCreateBySystem()) {
            getDropSuggestion()
        } else {
            val itemId = dropProcessDataEntity.selectedInventory?.itemId?:""
            val uomId = dropProcessDataEntity.selectedInventory?.uomId?:""
            getItemUom(itemId, uomId)
        }
    }

    private fun autoUpdateDataToUi() {
        mapDataToUi(ReplenishmentDropSubmitDataState::needPrintLp, ReplenishmentDropSubmitUiState::needPrintLp){
            it
        }
        mapDataToUi(ReplenishmentDropSubmitDataState::isEntireDrop, ReplenishmentDropSubmitUiState::isEntireDrop){
            it
        }
        mapDataToUi(ReplenishmentDropSubmitDataState::toLocation, ReplenishmentDropSubmitUiState::toLocation){
            it
        }
        mapDataToUi(ReplenishmentDropSubmitDataState::dropSuggestion, ReplenishmentDropSubmitUiState::dropSuggestion){
            it
        }
        mapDataToUi(ReplenishmentDropSubmitDataState::isBaseUom, ReplenishmentDropSubmitUiState::isBaseUom){
            it
        }
        mapDataToUi(ReplenishmentDropSubmitDataState::selectUom, ReplenishmentDropSubmitUiState::selectUom){
            it
        }
        mapDataToUi(ReplenishmentDropSubmitDataState::canEntireLpDrop, ReplenishmentDropSubmitUiState::canEntireLpDrop){
            it
        }
    }

    private fun getItemUom(itemId: String, uomId: String) {
        launch {
            val itemUomResult = requestAwait(repository.searchItemUom(itemId))
            if (itemUomResult.isSuccess) {
                val itemUomList = itemUomResult.getOrNull()
                val isBaseUom = itemUomList?.size == 1 && itemUomList.firstOrNull()?.isBaseUom == true
                val selectUom = itemUomList?.find { it.uomId == uomId }
                setDataState { copy(itemUomList = itemUomList, isBaseUom = isBaseUom, selectUom = selectUom) }
            }
        }
    }

    fun setSelectUom(uomEntity: UomEntity?) {
        setDataState { copy(selectUom = uomEntity) }
    }

    fun scannedLocation(data: String?, onMultiLocation: (List<LocationEntity>) -> Flow<LocationEntity?>) {
        if (data.isNullOrEmpty()) {
            ToastUtil.showToast(R.string.msg_please_scan_or_input_location)
            return
        }
        launch {
            requestAwait(repository.searchLocation(data)).onSuccess { selectLocation(it, onMultiLocation) }
        }
    }

    private suspend fun selectLocation(locations: List<LocationEntity>?, onMultiLocation: (List<LocationEntity>) -> Flow<LocationEntity?>) {
        if (locations == null) {
            ToastUtil.showToast(R.string.location_not_found)
        } else {
            val locationEntity = when (locations.safeCount()) {
                0 -> {
                    ToastUtil.showToast(R.string.location_not_found)
                    null
                }
                1 -> locations.first()
                else -> onMultiLocation(locations).firstOrNull()
            } ?: return
            dropProcessDataEntity.updateToLocation(locationEntity)
            setDataState { copy(toLocation = locationEntity, canEntireLpDrop = !locationEntity.isPickLocation) }
            setNeedPrintLp(dataState.isEntireDrop)
        }
    }

    fun getDropToLocationName(): String? {
        return dropProcessDataEntity.toLocation?.name
    }

    fun getDropItemOrLp(): String? {
        return if (dropProcessDataEntity.isCollectFromEntire()) {
            dropProcessDataEntity.selectedInventory?.lpId
        } else {
            dropProcessDataEntity.selectedInventory?.itemName
        }
    }

    fun getItemQty(): String {
        return (dropProcessDataEntity.selectedInventory?.qty ?: "").toString()
    }

    fun setNeedPrintLp(isEntireDrop: Boolean) {
        var needPrintLp = true
        if (dropProcessDataEntity.toLocation?.isPickLocation == true) {
            needPrintLp = false
        }
        if (dropProcessDataEntity.isCollectFromEntire() && isEntireDrop) {
            needPrintLp = false
        }
        setDataState { copy(needPrintLp = needPrintLp) }
    }

    fun setIsEntireDrop(isEntireDrop: Boolean) {
        setDataState { copy(isEntireDrop = isEntireDrop) }
        dropProcessDataEntity.updateIsEntireDrop(isEntireDrop)
    }

    private fun getIsEntireDrop() = dataState.isEntireDrop

    private fun isNeedPrintLp() = dataState.needPrintLp

    fun setPrintedLp(lpId: String?) {
        dropProcessDataEntity.updatePrintLpId(lpId)
    }

    fun drop(qty: String?) {
        dropProcessDataEntity.updateQty(qty)
        ReplenishmentValidator.validateDrop(dropProcessDataEntity) { code, msg ->
            when (code) {
                ReplenishmentValidator.PASS_VALIDATE -> dropSubmit()
                ReplenishmentValidator.SHOW_ERR_MSG ->  showSnack(SnackType.ErrorV2(), msg)
            }
        }
    }

    private fun dropSubmit() {
        launch {
            requestAwait(repository.drop(buildDropCreate())).onSuccess {
                if (isNeedPrintLp()) {
                    bindLocationToLp()
                }
                showSnack(SnackType.SuccessV1(), R.string.inventory_drop_success)
                dropProcessDataEntity.clear()
                activityViewModel.onBackToDropFromFragment()
            }
        }
    }

    private fun bindLocationToLp() {
        val updateLpRequestEntity = LpRequestEntity().apply {
            this.locationId = dropProcessDataEntity.toLocation?.id
            this.id = dropProcessDataEntity.printedLp
        }
        launch {
            requestAwait(repository.updateLp(updateLpRequestEntity))
        }
    }

    private fun buildDropCreate(): DropCreateEntity {
        return DropCreateEntity().apply {
            this.taskId = dropProcessDataEntity.replenishmentTaskEntity.id
            this.stepId = dropProcessDataEntity.replenishmentTaskEntity.getStepId()?.toLong()
            this.itemId = dropProcessDataEntity.selectedInventory?.itemId
            this.titleId = dropProcessDataEntity.selectedInventory?.titleId
            this.fromLpId = if (dropProcessDataEntity.isCollectFromEntire()) dropProcessDataEntity.selectedInventory?.lpId
                            else dropProcessDataEntity.replenishmentTaskEntity.hlpId
            this.fromUomId = dropProcessDataEntity.selectedInventory?.uomId
            this.toLPId = generateToLpId()
            this.toLocationId = dropProcessDataEntity.toLocation?.id
            this.toUomId = dataState.selectUom?.uomId
            this.isEntireLPReplenish = dropProcessDataEntity.isEntireDrop
            if (dropProcessDataEntity.isEntireDrop) {
                this.dropQty = dropProcessDataEntity.selectedInventory?.qty
            } else {
                this.dropQty = dropProcessDataEntity.qty?.toDoubleOrNull()
            }
        }
    }

    private fun generateToLpId(): String? {
        if (isNeedPrintLp()) {
            return dropProcessDataEntity.printedLp
        }
        return if (dropProcessDataEntity.toLocation?.isPickLocation == true) {
            dropProcessDataEntity.toLocation?.hlpId
        } else {
            dropProcessDataEntity.selectedInventory?.lpId
        }
    }

    private fun getDropSuggestion() {
        val taskId = activityViewModel.getTaskId()
        taskId?: return
        launch {
            requestAwait(repository.getDropSuggestion(taskId)).onSuccess {
                it?.let {
                    setDataState { copy(dropSuggestion = it) }
                    awaitDataState()
                    if (it.itemId.isNotEmpty() && it.uomId.isNotEmpty()) {
                        getItemUom(it.itemId, it.uomId)
                    }
                }
            }
        }
    }
}


private class Repository : BaseRepository() {

    val replenishmentTaskApi by apiServiceLazy<ReplenishmentTaskApi>()
    val lpApi by apiServiceLazy<LpApiService>()
    val locationApi by apiServiceLazy<LocationApiService>()
    val itemUomApi by  apiServiceLazy<ItemUomApi>()

    fun searchLocation(locationName: String) = rxRequest2(locationApi.searchLocations(LocationRequestEntity().apply {
        this.name = locationName
    }))

    fun drop(dropCreateEntity: DropCreateEntity) = rxRequest2(replenishmentTaskApi.drop(dropCreateEntity))

    fun updateLp(lpRequestEntity: LpRequestEntity) = requestV2({lpApi.updateLp(lpRequestEntity)})

    fun getDropSuggestion(taskId: String) = rxRequest2(replenishmentTaskApi.getDropSuggestion(taskId))

    fun searchItemUom(itemId: String) = requestV2({ itemUomApi.searchItemUom(UomQueryEntity().apply {
        this.itemId = itemId
    })})
}