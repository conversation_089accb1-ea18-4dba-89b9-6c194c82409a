package com.unis.wms.pick_task.pick.work.fragments

import android.annotation.SuppressLint
import android.content.Intent
import android.os.Bundle
import android.view.Menu
import android.view.MenuInflater
import android.view.MenuItem
import androidx.core.content.ContextCompat
import com.customer.widget.common.CallbackDialogV1
import com.customer.widget.common.CenterDialog
import com.customer.widget.extensions.setVisibleOrGone
import com.linc.platform.print.model.LabelSizeEntry
import com.unis.platform.inventoryissue.model.InventoryIssueEntity
import com.unis.platform.location_override.model.LocationOverrideRequestEntity
import com.unis.platform.location_v2.model.LocationEntity
import com.unis.platform.pick_to_light.mode.light.LightColorType
import com.unis.platform.pick_v2.model.pick.PickStepStageLocationSuggestEntity
import com.unis.reactivemvi.common.collectDispatchToDeferred
import com.unis.reactivemvi.mvi.ReactiveFragment
import com.unis.reactivemvi.mvi.ReactiveViewScope
import com.unis.reactivemvi.mvi.onDeferredEvent
import com.unis.reactivemvi.mvi.onEvent
import com.unis.reactivemvi.mvvm.kotlin.androidx.lifecycle.runtime_ktx.lifecycleScope
import com.unis.reactivemvi.mvvm.kotlin.extensions.addFragment
import com.unis.reactivemvi.mvvm.kotlin.extensions.getActivityViewModel
import com.unis.reactivemvi.print.PrinterManager
import com.unis.reactivemvi.print.ui.PrintSettingActivityV2
import com.unis.wms.R
import com.unis.wms.databinding.FragmentPickStepWorkBinding
import com.unis.wms.location_override.LocationOverrideRequestWaitingDialog
import com.unis.wms.more.inventorysearch.InventorySearchActivity
import com.unis.wms.more.inventorysearch.InventorySearchConfig
import com.unis.wms.pick_task.PickTaskStepStartEvent
import com.unis.wms.pick_task.pick.light_event.PickToLightEventActivity
import com.unis.wms.pick_task.pick.return_inventory.PickReturnActivity
import com.unis.wms.pick_task.pick.work.PickStepViewModel
import com.unis.wms.pick_task.pick.work.QtyEditDialog
import com.unis.wms.pick_task.pick.work.dialog.InventoryReportDialog
import com.unis.wms.pick_task.pick.work.model.PickItemProgress
import com.unis.wms.pick_task.pick.work.model.PickStepButtonState
import com.unis.wms.pick_task.pick.work.model.PickStepProgress
import com.unis.wms.pick_task.pick.work.model.PickStepWorkUiEvent
import com.unis.wms.pick_task.pick.work.model.StepWorkProgress
import com.unis.wms.pick_task.pick.work.model.PickStepWorkUiState
import com.unis.wms.pick_task.pick.work.viewmodel.PickStepWorkViewModel
import com.unis.wms.pick_to_light.base.model.LightSocketMessage
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.emptyFlow
import org.greenrobot.eventbus.EventBus

class PickStepWorkFragment : ReactiveFragment<PickStepWorkViewModel, PickStepWorkUiState, FragmentPickStepWorkBinding>() {

    companion object {
        const val TAG = "PickWorkFragment"

        @JvmStatic
        fun newInstance() = PickStepWorkFragment()
    }

    private val activityViewModel by lazy { getActivityViewModel<PickStepViewModel>()!! }

    override fun createViewModel(): PickStepWorkViewModel = PickStepWorkViewModel(activityViewModel)

    override fun initView(savedInstanceState: Bundle?) {
        setHasOptionsMenu(true)
        binding?.apply {
            nextLocationBtn.setOnClickListener {
                viewModel.showOverrideLocationReportIssueDialog()
            }

            submitBtn.setOnClickListener {
                viewModel.submitAndNextLocation()
            }

            newLpBtn.setOnClickListener {
                viewModel.printNewLp(idmUserId, facilityName, ::showReprintConfirmDialog)
            }

            entireLpPickSb.setOnCheckedChangeListener { _, isChecked ->
                if (!entireLpPickSb.isCheckedUpdateFromOutside) {
                    viewModel.setEntireLpPick(isChecked)
                }
            }

            childFragmentManager.addOnBackStackChangedListener {
                val currentFragment = childFragmentManager.findFragmentById(R.id.pick_step_work_container_fl) ?: return@addOnBackStackChangedListener
                pickBackStepBtn.setVisibleOrGone(currentFragment !is PickScanLocationFragment)
                val currentWorkStep = when (currentFragment) {
                    is PickScanLocationFragment -> {
                        StepWorkProgress.ScanLocation
                    }

                    is PickScanLpFragment -> {
                        StepWorkProgress.ScanLp
                    }

                    is PickScanItemFragment -> {
                        StepWorkProgress.ScanItem
                    }

                    is PickScanSnFragment -> {
                        StepWorkProgress.ScanSN
                    }

                    is PickScanLotNoFragment -> {
                        StepWorkProgress.ScanLotNo
                    }

                    is PickScanToLPFragment -> {
                        StepWorkProgress.ScanToLP
                    }

                    else -> return@addOnBackStackChangedListener
                }
                viewModel.updateCurrentWorkStep(currentWorkStep)
            }
            pickBackStepBtn.setOnClickListener {
                if (childFragmentManager.backStackEntryCount > 1) {
                    childFragmentManager.popBackStack()
                }
            }

            minusQtyIv.setOnClickListener {
                viewModel.updatePickedQty(false)
            }
            plusQtyIv.setOnClickListener {
                viewModel.updatePickedQty(true)
            }
        }
        uiEvent()
        showCheckContainerDialog()
        selectPrinter()
        showCloseStepDialog()
    }

    @SuppressLint("SetTextI18n")
    override fun ReactiveViewScope.subscribeToUiState() {
        subscribe(PickStepWorkUiState::locationSuggest, ::updateSuggestLocation)
        subscribe(PickStepWorkUiState::lightColor, ::updateTaskLightColorBg)
        subscribe(PickStepWorkUiState::cartonNo) {
            binding?.containerNoTv?.setVisibleOrGone(!it.isNullOrEmpty())
            binding?.containerNoTv?.text = "Container#: $it"
        }
        subscribe(PickStepWorkUiState::isShowChangeQty) {
            binding?.apply {
                minusQtyIv.setVisibleOrGone(it)
                plusQtyIv.setVisibleOrGone(it)
                if (it) {
                    pickedQtyTv.setOnClickListener {
                        showQtyEditDialog()
                    }
                } else {
                    pickedQtyTv.setOnClickListener(null)
                }
                if (it) {
                    pickedQtyTv.setBackgroundResource(R.drawable.rect_color_bg_grey_r4)
                } else {
                    pickedQtyTv.setBackgroundResource(0)
                }
            }
        }
        subscribe(PickStepWorkUiState::isEntirePick) {
            if (binding?.entireLpPickSb?.isChecked != it) {
                binding?.entireLpPickSb?.isChecked = it
            }
        }
        subscribe(PickStepWorkUiState::pickStepProgress, ::updatePickStepProgress)
        subscribe(PickStepWorkUiState::pickItemProgress, ::updatePickItemProgress)
        subscribe(PickStepWorkUiState::pickStepButtonState, ::updatePickStepButtonState)
        subscribe(PickStepWorkUiState::enablePickToLight){
            activity?.invalidateOptionsMenu()
        }
        subscribe(PickStepWorkUiState::stageLocationSuggest, ::updateStageSuggestLocation)
    }

    private fun updateSuggestLocation(location: LocationEntity?) {
        if (location == null) return
        binding?.apply {
            suggestLocationTv.text = location.name
        }
    }

    @SuppressLint("SetTextI18n")
    private fun updateStageSuggestLocation(stageSuggestLocations: List<PickStepStageLocationSuggestEntity>?) {
        binding?.apply {
            stageLocationSuggestLl.setVisibleOrGone(!stageSuggestLocations.isNullOrEmpty())
            val locationNameStr = stageSuggestLocations?.mapNotNull { loc -> loc.name }?.joinToString(", ")
            stageSuggestLocationTv.text = "${getString(R.string.suggest_stage_location)} $locationNameStr"
        }
    }

    private fun uiEvent() {
        showStepFragments()
        showOverrideInventoryIssueDialog()
        showLocationOverrideWaitingApprovalDialog()
        popStepFragment()
        popAllStepFragment()
        onEvent<PickStepWorkUiEvent.UpdateConnectStatus> {
            activity?.invalidateOptionsMenu()
        }
        showResendLightMessageDialog()
    }

    private fun showStepFragments() = onEvent<PickStepWorkUiEvent.StepWorkNavigate> {
        val currentFragment = childFragmentManager.findFragmentById(R.id.pick_step_work_container_fl)
        when (step) {
            StepWorkProgress.ScanLocation -> {
                if (currentFragment is PickScanLocationFragment) return@onEvent
                addFragment(R.id.pick_step_work_container_fl, PickScanLocationFragment.newInstance(), PickScanLocationFragment.TAG, true)
            }

            StepWorkProgress.ScanLp -> {
                if (currentFragment is PickScanLpFragment) return@onEvent
                addFragment(R.id.pick_step_work_container_fl, PickScanLpFragment.newInstance(), PickScanLpFragment.TAG, true)
            }

            StepWorkProgress.ScanItem -> {
                if (currentFragment is PickScanItemFragment) return@onEvent
                addFragment(R.id.pick_step_work_container_fl, PickScanItemFragment.newInstance(), PickScanItemFragment.TAG, true)
            }

            StepWorkProgress.ScanSN -> {
                if (currentFragment is PickScanSnFragment) return@onEvent
                addFragment(R.id.pick_step_work_container_fl, PickScanSnFragment.newInstance(), PickScanSnFragment.TAG, true)
            }

            StepWorkProgress.ScanLotNo -> {
                if (currentFragment is PickScanLotNoFragment) return@onEvent
                addFragment(R.id.pick_step_work_container_fl, PickScanLotNoFragment.newInstance(), PickScanLotNoFragment.TAG, true)
            }

            StepWorkProgress.ScanToLP -> {
                if (currentFragment is PickScanToLPFragment) return@onEvent
                addFragment(R.id.pick_step_work_container_fl, PickScanToLPFragment.newInstance(), PickScanToLPFragment.TAG, true)
            }
        }
    }

    private fun showOverrideInventoryIssueDialog() = onEvent<PickStepWorkUiEvent.ShowOverrideInventoryIssueDialog> {
        InventoryReportDialog(context, issues, object : InventoryReportDialog.OnIssueItemClickListener {
            override fun onClick(inventoryIssue: InventoryIssueEntity?) {
                viewModel.overrideLocationSuggest(null, inventoryIssue, false)
            }

            override fun onSkipForNowClick() {
                viewModel.overrideLocationSuggest(null, null, true)
            }
        }).build().show()
    }

    private fun showLocationOverrideWaitingApprovalDialog() = onEvent<PickStepWorkUiEvent.ShowLocationOverrideWaitingApprovalDialog> {
        val dialog = LocationOverrideRequestWaitingDialog.newInstance(overrideRequest)
        dialog.setOverrideRequestResultListener(object: LocationOverrideRequestWaitingDialog.OverrideRequestResultListener{
            override fun onApproval(request: LocationOverrideRequestEntity) {
                viewModel.overrideLocationSuggest(
                    specificLocation = specificLocation,
                    inventoryIssue = inventoryIssue,
                    isNeedWaitingApproval = false,
                    overrideRequestResult = request
                )
            }
        })
        dialog.show(childFragmentManager, LocationOverrideRequestWaitingDialog.TAG)
    }

    private fun updatePickStepButtonState(buttonState: PickStepButtonState?) {
        buttonState?.apply {
            binding?.entireLpPickLl?.setVisibleOrGone(showEntireLPPick)
            binding?.submitBtn?.setVisibleOrGone(showSubmitButton)
            binding?.newLpBtn?.setVisibleOrGone(showNewLPButton)
        }
    }

    @SuppressLint("SetTextI18n")
    private fun updatePickStepProgress(pickStepProgress: PickStepProgress?) {
        binding?.apply {
            pickStepProgress?.apply {
                progressQtyTv.text = String.format(getString(R.string.text_picked_sku_ea), "$skuQty / $skuTotal", "$picked / $totalQty")
                pickStepProgressPb.progress = progress.toInt()
            }
        }
    }

    @SuppressLint("SetTextI18n")
    private fun updatePickItemProgress(pickItemProgress: PickItemProgress?) {
        binding?.apply {
            pickItemProgress?.apply {
                pickedQtyTv.text = pickingQty.toString()
                pickQtyTv.text = "/ $totalQty $uom"
                pickItemProgressPb.progress = progress.toInt()
            }
        }
    }

    private fun popStepFragment() = onEvent<PickStepWorkUiEvent.PopStepFragmentEvent> {
        if (childFragmentManager.backStackEntryCount > 1) {
            childFragmentManager.popBackStack()
        }
    }

    private fun popAllStepFragment() = onEvent<PickStepWorkUiEvent.PopAllStepFragmentEvent> {
        if (childFragmentManager.backStackEntryCount > 0) {
            for (i in 0 until childFragmentManager.backStackEntryCount) {
                childFragmentManager.popBackStackImmediate()
            }
        }
    }

    private fun updateTaskLightColorBg(colorType: LightColorType?) {
        val colorRes = when (colorType) {
            LightColorType.R -> R.color.accent_red_v1
            LightColorType.W -> R.color.white
            LightColorType.Y -> R.color.color_facc15
            LightColorType.B -> R.color.qmui_config_color_blue
            LightColorType.S -> R.color.color_2FC2E6
            LightColorType.P -> R.color.color_9645D2
            LightColorType.G -> R.color.green_56C288
            else -> R.color.page_background_v1
        }
        binding?.apply {
            suggestLocationTv.setBackgroundColor(ContextCompat.getColor(context!!, colorRes))
            if (colorType == LightColorType.W) {
                suggestLocationTv.setTextColor(ContextCompat.getColor(context!!, R.color.black))
                pickItemQtyLayout.setBackgroundColor(ContextCompat.getColor(context!!, R.color.pick_task_grey))
            } else {
                suggestLocationTv.setTextColor(ContextCompat.getColor(context!!, R.color.white))
                pickItemQtyLayout.setBackgroundColor(ContextCompat.getColor(context!!, R.color.white))
            }
        }
    }

    private fun showCheckContainerDialog() = onDeferredEvent<PickStepWorkUiEvent.ShowCartonCheckDialog, Boolean> {
        context ?: return@onDeferredEvent
        val message = if (facility2.facilitySettingEntity?.autoReturnToLocation == true) {
            getString(R.string.msg_carton_check_content)
        } else {
            getString(R.string.msg_carton_check_content_2)
        }

        CallbackDialogV1.showConfirm(
            context!!,
            title = getString(R.string.label_empty_carton_detected_confirm),
            message = message,
            cancelable = false,
            positiveText = getString(R.string.label_empty),
            negativeText = getString(R.string.label_not_empty)).collectDispatchToDeferred(lifecycleScope, it)
    }

    private fun showQtyEditDialog() {
        context ?: return
        val itemQty = viewModel.dataState.pickWorkData?.pickingQty ?: 0
        val itemUom = viewModel.dataState.selectedPickItemSuggest?.uom ?: "EA"
        QtyEditDialog(context!!, itemUom, itemQty) { qty ->
            viewModel.updatePickingQty(qty)
        }.show()
    }

    private fun showReprintConfirmDialog(message: String): Flow<Boolean?> {
        context ?: return emptyFlow()
        return CallbackDialogV1.showConfirm(
            context = context, title = getString(R.string.text_print_fail), message = getString(R.string.text_please_reprint) + "\r\n" + message)
    }

    override fun onCreateOptionsMenu(menu: Menu?, inflater: MenuInflater?) {
        inflater?.inflate(R.menu.fragment_pick_step_menu_action, menu)
        if (viewModel.dataState.pickToLightServerStatus != -1 && viewModel.dataState.enablePickToLight) {
            menu?.add("")?.setIcon(if (viewModel.dataState.pickToLightServerStatus == 1) R.drawable.icon_websocket_in else R.drawable.icon_websocket_out)
                ?.setShowAsAction(MenuItem.SHOW_AS_ACTION_IF_ROOM)
        }
        super.onCreateOptionsMenu(menu, inflater)
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        context?.let {
            when (item.itemId) {
                R.id.action_force_close_step -> {
                    activityViewModel.forceCloseStep(this::forceCloseStep)
                }

                R.id.action_print_setting -> {
                    PrintSettingActivityV2.startActivity(
                        it, PrintSettingActivityV2.Params.SelectionMode(
                            selected = PrinterManager.getPrinter(idmUserId, facilityName, LabelSizeEntry.TWO_ONE)))
                }

                R.id.action_return_inventory -> {
                    activityViewModel.dataState.pickTask?.let { pickTask ->
                        val pickTaskStepStartEvent = PickTaskStepStartEvent().apply {
                            taskEntity = pickTask
                        }
                        EventBus.getDefault().postSticky(pickTaskStepStartEvent)
                        PickReturnActivity.startActivity(it)
                    }
                }

                R.id.action_label_inventory_search -> {
                    InventorySearchActivity.startActivity(context!!, InventorySearchConfig().apply {
                        this.itemId = viewModel.dataState.selectedPickItemSuggest?.id
                        this.customerId = viewModel.dataState.selectedPickItemSuggest?.customerId
                    })
                }

                R.id.pick_to_light_event -> {
                    startActivity(Intent(context, PickToLightEventActivity::class.java))
                }

                R.id.action_rebuild_pick_strategy -> {
                    viewModel.rebuildPickStrategyByTask()
                }

                R.id.action_visible_or_gone_submit-> {
                    viewModel.setForceShowSubmitButton()
                }

                else -> {}
            }
        }
        return super.onOptionsItemSelected(item)
    }

    private fun forceCloseStep() = CallbackDialogV1.showConfirm(context!!, getString(R.string.force_close_step))

    private fun selectPrinter() = onEvent<PickStepWorkUiEvent.StartPrinterSettingActivity> {
        PrintSettingActivityV2.startActivity(context!!)
    }

    private fun showCloseStepDialog() = onEvent<PickStepWorkUiEvent.ShowClosePickStepDialog> {
        context ?: return@onEvent
        CenterDialog.confirm(context = context!!, positiveClick = {
            activityViewModel.closeStep()
        }, message = getString(R.string.query_close_step)).show()
    }

    private fun showResendLightMessageDialog() = onEvent<PickStepWorkUiEvent.ShowResendLightMessageDialog> {
        context ?: return@onEvent
        CenterDialog.confirm(context = context!!, positiveClick = {
            if (message is LightSocketMessage) {
                viewModel.resendLocationLightMessage(message = message.apply {
                    sendFrom = "resend message"
                })
            }
        }, message = getString(R.string.msg_resend_pick_to_light), positiveText = getString(R.string.text_retry)).show()
    }
}