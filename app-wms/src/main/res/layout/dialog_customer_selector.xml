<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:padding="16dp"
    android:background="@color/page_background_v1">

    <TextView
        android:id="@+id/dialog_title"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="@string/text_select_customer"
        android:textColor="@color/white"
        android:textSize="18sp"
        android:textStyle="bold"
        android:layout_marginBottom="16dp" />

    <EditText
        android:id="@+id/search_edit_text"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/bg_rect_radius_4"
        android:hint="@string/text_search_customer_by_name"
        android:padding="12dp"
        android:textColor="@color/white"
        android:textColorHint="#80FFFFFF"
        android:textSize="16sp"
        android:imeOptions="actionSearch"
        android:inputType="text"
        android:maxLines="1"
        android:layout_marginBottom="16dp" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/customer_recycler_view"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:clipToPadding="false"
        android:padding="8dp"
        android:maxHeight="400dp" />

</LinearLayout>
