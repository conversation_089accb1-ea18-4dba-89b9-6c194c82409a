package com.unis.platform.wcs.model

import com.google.gson.annotations.SerializedName
import com.unis.platform.common.model.task.TaskType
import com.unis.platform.location_v2.model.LocationType
import com.unis.platform.pick_v2.model.PickMethod
import com.unis.platform.pick_v2.model.PickType
import java.util.Date

/**
 * 作业创建命令实体类
 */
data class JobCreateEntity(
    /**
     * 命令ID
     */
    @SerializedName("commandId")
    val commandId: String? = null,
    
    /**
     * 命令类型
     */
    @SerializedName("commandType")
    val commandType: String? = null,
    
    /**
     * 命令内容
     */
    @SerializedName("content")
    val content: String? = null,
    
    /**
     * 扩展数据
     */
    @SerializedName("extensionData")
    val extensionData: Map<String, Any>? = null,

    /**
     * 业务动作ID
     */
    @SerializedName("businessActionId")
    val businessActionId: String,

    /**
     * 动作类型
     */
    @SerializedName("actionType")
    val actionType: WcsActionType,

    /**
     * 下一个动作类型
     */
    @SerializedName("nextActionType")
    val nextActionType: WcsActionType? = null,

    /**
     * 目标站点代码
     */
    @SerializedName("toStationCode")
    val toStationCode: String? = null,

    /**
     * 拣货方式
     */
    @SerializedName("pickMethod")
    val pickMethod: PickMethod? = null,

    /**
     * 拣货类型
     */
    @SerializedName("pickType")
    val pickType: PickType,

    /**
     * 业务ID
     */
    @SerializedName("businessId")
    val businessId: String,

    /**
     * 优先级
     */
    @SerializedName("priority")
    val priority: Int? = null,

    /**
     * 物品ID
     */
    @SerializedName("itemId")
    val itemId: String,

    /**
     * 物品代码
     */
    @SerializedName("itemCode")
    val itemCode: String,

    /**
     * 物品名称
     */
    @SerializedName("itemName")
    val itemName: String,

    /**
     * 订单ID
     */
    @SerializedName("orderId")
    val orderId: String? = null,

    /**
     * 批次号
     */
    @SerializedName("lotNo")
    val lotNo: String? = null,

    /**
     * 起始位置名称
     */
    @SerializedName("fromLocationName")
    val fromLocationName: String,

    /**
     * 起始位置ID
     */
    @SerializedName("fromLocationId")
    val fromLocationId: String? = null,

    /**
     * 起始位置类型
     */
    @SerializedName("fromLocationType")
    val fromLocationType: LocationType? = null,

    /**
     * 目标位置ID
     */
    @SerializedName("toLocationId")
    val toLocationId: String? = null,

    /**
     * 目标位置名称
     */
    @SerializedName("toLocationName")
    val toLocationName: String? = null,

    /**
     * 目标位置类型
     */
    @SerializedName("toLocationType")
    val toLocationType: LocationType? = null,

    /**
     * 计量单位ID
     */
    @SerializedName("uomId")
    val uomId: String,

    /**
     * 数量
     */
    @SerializedName("qty")
    val qty: Double,

    /**
     * 基本数量
     */
    @SerializedName("baseQty")
    val baseQty: Double,

    /**
     * 是否为次要作业
     */
    @SerializedName("isSecondaryJob")
    val isSecondaryJob: Boolean? = null,

    /**
     * WMS任务类型
     */
    @SerializedName("wmsTaskType")
    val wmsTaskType: String? = null,

    /**
     * 所需能力
     */
    @SerializedName("requiredCapabilities")
    val requiredCapabilities: List<String>? = null,

    /**
     * 依赖的WMS动作ID列表
     */
    @SerializedName("dependentWmsActionIds")
    val dependentWmsActionIds: List<String>? = null,

    /**
     * 父WMS动作ID
     */
    @SerializedName("parentWmsActionId")
    val parentWmsActionId: String? = null,

    /**
     * WMS分组ID
     */
    @SerializedName("wmsGroupId")
    val wmsGroupId: String? = null,

    /**
     * 设备类型
     */
    @SerializedName("equipmentType")
    val equipmentType: String? = null,

    /**
     * 截止时间
     */
    @SerializedName("dueTime")
    val dueTime: Date? = null,

    /**
     * 起始托盘ID
     */
    @SerializedName("fromLPId")
    val fromLPId: String? = null,

    /**
     * 起始托盘ID列表
     */
    @SerializedName("fromLPIds")
    val fromLPIds: List<String>? = null,

    /**
     * 托盘数量
     */
    @SerializedName("palletCount")
    val palletCount: Int? = null,

    /**
     * 扩展JSON
     */
    @SerializedName("extensionJson")
    val extensionJson: Map<String, Any>? = null,

    /**
     * 指派的用户ID
     */
    @SerializedName("assigneeUserId")
    val assigneeUserId: String? = null,

    /**
     * 分配模式
     */
    @SerializedName("assignmentMode")
    val assignmentMode: AssignmentMode = AssignmentMode.EQUIPMENT,

    /**
     * 开始时间
     */
    @SerializedName("startTime")
    val startTime: Date? = null,

    /**
     * 结束时间
     */
    @SerializedName("endTime")
    val endTime: Date? = null,

    /**
     * 客户ID
     */
    @SerializedName("customerId")
    val customerId: String? = null,

    /**
     * 波次号
     */
    @SerializedName("waveNo")
    val waveNo: String? = null,

    /**
     * 批次号
     */
    @SerializedName("batchNo")
    val batchNo: String? = null,

    /**
     * 缩写
     */
    @SerializedName("abbreviation")
    val abbreviation: String? = null,

    /**
     * UPC编码
     */
    @SerializedName("upcCode")
    val upcCode: String? = null,

    /**
     * 箱UPC编码
     */
    @SerializedName("upcCodeCase")
    val upcCodeCase: String? = null,

    /**
     * EAN编码
     */
    @SerializedName("eanCode")
    val eanCode: String? = null,

    /**
     * 动态文本值01
     */
    @SerializedName("dynTxtValue01")
    val dynTxtValue01: String? = null,

    /**
     * 动态文本值02
     */
    @SerializedName("dynTxtValue02")
    val dynTxtValue02: String? = null,

    /**
     * 动态文本值03
     */
    @SerializedName("dynTxtValue03")
    val dynTxtValue03: String? = null,

    /**
     * 动态文本值04
     */
    @SerializedName("dynTxtValue04")
    val dynTxtValue04: String? = null,

    /**
     * 动态文本值05
     */
    @SerializedName("dynTxtValue05")
    val dynTxtValue05: String? = null,

    /**
     * 动态文本值06
     */
    @SerializedName("dynTxtValue06")
    val dynTxtValue06: String? = null,

    /**
     * 动态文本值07
     */
    @SerializedName("dynTxtValue07")
    val dynTxtValue07: String? = null,

    /**
     * 动态文本值08
     */
    @SerializedName("dynTxtValue08")
    val dynTxtValue08: String? = null,

    /**
     * 动态文本值09
     */
    @SerializedName("dynTxtValue09")
    val dynTxtValue09: String? = null,

    /**
     * 动态文本值10
     */
    @SerializedName("dynTxtValue10")
    val dynTxtValue10: String? = null,

    /**
     * 动态文本值11
     */
    @SerializedName("dynTxtValue11")
    val dynTxtValue11: String? = null,

    /**
     * 动态文本值12
     */
    @SerializedName("dynTxtValue12")
    val dynTxtValue12: String? = null,

    /**
     * 动态文本值13
     */
    @SerializedName("dynTxtValue13")
    val dynTxtValue13: String? = null,

    /**
     * 动态文本值14
     */
    @SerializedName("dynTxtValue14")
    val dynTxtValue14: String? = null,

    /**
     * 动态文本值15
     */
    @SerializedName("dynTxtValue15")
    val dynTxtValue15: String? = null,

    /**
     * 标题ID
     */
    @SerializedName("titleId")
    val titleId: String? = null,

    /**
     * 商品类型
     */
    @SerializedName("goodsType")
    val goodsType: String? = null
) 