package com.unis.platform.wcs

import com.linc.platform.http.BaseResponse
import com.linc.platform.http.HttpService
import com.unis.platform.common.model.PageResponseEntity
import com.unis.platform.wcs.model.WcsActionSubmitEntity
import com.unis.platform.wcs.model.WcsActionSubmitResultEntity
import com.unis.platform.wcs.model.WcsActionJobEntity
import com.unis.platform.wcs.model.WcsTaskActionEntity
import com.unis.platform.wcs.model.WcsTaskActionQueryEntity
import com.unis.platform.wcs.model.WcsTaskActionRequest
import retrofit2.Response
import retrofit2.http.Body
import retrofit2.http.POST
import retrofit2.http.Path

interface WcsLocalApiService {

    companion object {
        @JvmStatic
        fun createService(protocol: String?, ip: String?, port: String?): WcsLocalApiService? =
            HttpService.createServiceWithoutCert("${protocol ?: ""}://${ip ?: ""}${port ?: ""}/", WcsLocalApiService::class.java, null)
    }

    @POST("task-action/assign")
    suspend fun getTaskAction(@Body request: WcsTaskActionRequest): Response<BaseResponse<WcsTaskActionEntity>>

    @POST("task-action/{cmdId}/submit")
    suspend fun submitTaskActionResult(
        @Path("cmdId") cmdId: String,
        @Body request: WcsActionSubmitEntity
    ): Response<BaseResponse<WcsActionSubmitResultEntity>>

    @POST("task-action/search-by-paging")
    suspend fun searchByPaging(@Body query: WcsTaskActionQueryEntity): Response<BaseResponse<PageResponseEntity<WcsActionJobEntity>>>
}