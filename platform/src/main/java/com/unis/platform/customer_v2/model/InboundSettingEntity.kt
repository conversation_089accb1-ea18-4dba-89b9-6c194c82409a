package com.unis.platform.customer_v2.model

import com.unis.platform.location_v2.model.LocationType
import com.unis.platform.lp_v2.model.ReceiveMethodEntity
import com.unis.platform.put_away.model.LpVerifyBeforePutAwayEntity

class InboundSettingEntity {

    val notAllowForceCloseForReceiving: Boolean? = null

    val receiptUniqueKeys: List<String>? = null

    val notAllowPartialItemReceive: Boolean? = null

    val notAllowOverItemReceive: Boolean? = null

    val notAllowPartialMaterialReceive: Boolean? = null

    val notAllowOverMaterialReceive: Boolean? = null

    val putAwayLocationTypes: List<PutAwayLocationType>? = null

    val allowReceiveLocationTypes: List<LocationType>? = null

    val enableSnCheckByPreProvidedSnFile: Boolean? = null

    val forceDefaultUOMReceiving: Boolean? = null

    val askForCapturingMaterial: Boolean? = null

    val preProvidedSnCheckSupplierFacilities: List<SupplierFacilitySettingEntity>? = null

    val allowReceiveMethods: List<ReceiveMethodEntity>? = null

    val specialGoodsType: List<String>? = null

    val forbidMultipleLpConfigAtReceiveTask: Boolean? = null

    val forbidCreateLpConfigAtReceiveTask: Boolean? = null

    val forbidCloseReceiveTaskBeforePutAway: Boolean? = null

    val forbidLpSetupPalletization: Boolean? = null

    val forceCollectItemInfoAtReceiving: Boolean? = null

    val collectTotalHours: Boolean? = null
    
    val collectOffloadWorks: Boolean? = null
    
    val allowNewUomInReceiving: Boolean? = null
    
    val skipStage: Boolean? = null

    val allowNewItemInReceiving: Boolean? = null

    val receiveQtyChecks: List<ReceiveQtyCheckEntity>? = null

    val collectPalletNoOnReceiving: Boolean? = null

    val skipPutAwayOnReceive: Boolean? = null

    val lpVerifyBeforePutAway: LpVerifyBeforePutAwayEntity? = null

    val confirmCapacityAfterPutAway: Boolean? = null

}

enum class PutAwayLocationType {
    CUSTOMER_VLG,
    ITEM_VLG,
    ITEM_GROUP_VLG,
    TITLE_VLG
}