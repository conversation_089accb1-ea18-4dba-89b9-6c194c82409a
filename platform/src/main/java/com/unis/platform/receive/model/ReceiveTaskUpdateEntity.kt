package com.unis.platform.receive.model

import com.unis.platform.common.model.task.TaskStatus
import com.unis.platform.common.model.task.TaskPriority
import java.util.Date

data class ReceiveTaskUpdateEntity(
    var id: String,
    var receiptIds: List<String>? = null,
    var customerId: String? = null,
    var status: TaskStatus? = null,
    var sysNote: String? = null,
    var note: String? = null,
    var tags: List<String>? = null,
    var preAssigneeUserId: String? = null,
    var assigneeUserId: String? = null,
    var dockId: String? = null,
    var entryId: String? = null,
    var priority: TaskPriority? = null,
    var dockCheckInTime: Date? = null,
    var dockCheckOutTime: Date? = null,
    var startTime: Date? = null,
    var endTime: Date? = null,
    var collectHoursInfos: List<HoursInfoEntity>? = null,
    var jobCode: String? = null,
    var jobCodeDescription: String? = null
) 