package com.unis.platform.cyclecount.model

import com.google.gson.annotations.SerializedName
import java.io.Serializable

data class ItemEntity(

    @SerializedName("id")
    val id: String,

    @SerializedName("name")
    val name: String,

    @SerializedName("upcCode")
    val upcCode: String,

    @SerializedName("upcCodeCase")
    val upcCodeCase: String,

    @SerializedName("eanCode")
    val eanCode: String,

    @SerializedName("requireCollectLotNoOnReceive")
    val requireCollectLotNo: Boolean,

    @SerializedName("requireCollectExpirationDateOnReceive")
    val requireCollectExpirationDate: Boolean,

    @SerializedName("requireCollectMfgDateOnReceive")
    val requireCollectMfgDate: Boolean,

    @SerializedName("requireCollectShelfLifeDaysOnReceive")
    val requireCollectShelfLifeDays: Boolean,

    @SerializedName("requireCollectSnOnReceive")
    val requireCollectSN: Boolean,

): Serializable