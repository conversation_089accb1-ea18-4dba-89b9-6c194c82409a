package com.unis.platform.cyclecount.model

import com.google.gson.annotations.SerializedName
import com.unis.platform.common.model.PageQueryEntity
import java.io.Serializable
import java.util.*

class CountResultCreateEntity(

    @SerializedName("id")
    var id: String? = null,

//    @SerializedName("ticketId")
//    var ticketId: String? = null,
//
//    @SerializedName("taskId")
//    var taskId: String? = null,

    @SerializedName("locationId")
    var locationId: String? = null,

    @SerializedName("isEmptyLocation")
    var isEmptyLocation: Boolean? = null,

    @SerializedName("isEmptyItem")
    var isEmptyItem: Boolean? = null,

    @SerializedName("countPalletQty")
    var countPalletQty: Double? = null,

    @SerializedName("itemId")
    var itemId: String? = null,

    @SerializedName("countItemQty")
    var countItemQty: Double? = null,

    @SerializedName("countUomId")
    var countUomId: String? = null,

    @SerializedName("uom")
    var uom: String? = null,

    @SerializedName("lpId")
    var lpId: String? = null,

    @SerializedName("lotNo")
    var lotNo: String? = null,

    @SerializedName("expirationDate")
    var expirationDate: Date? = null,

    @SerializedName("mfgDate")
    var mfgDate: Date? = null,

    @SerializedName("shelfLifeDays")
    var shelfLifeDays: Int? = null,

    @SerializedName("sn")
    var sn: String? = null,
) : Serializable