package com.unis.platform.cyclecount.model

import com.google.gson.annotations.SerializedName
import java.io.Serializable
import java.util.*

data class CountInventoryEntity(

    @SerializedName("id")
    val id: String,

    @SerializedName("locationId")
    val locationId: String,

    @SerializedName("locationName")
    val locationName: String,

    @SerializedName("itemId")
    val itemId: String,

    @SerializedName("itemName")
    val itemName: String,

    @SerializedName("qty")
    var qty: Double,

    @SerializedName("uomId")
    val uomId: String,

    @SerializedName("uom")
    val uom: String?,

    @SerializedName("baseQty")
    var baseQty: Double,

    @SerializedName("lpId")
    val lpId: String,

    @SerializedName("lotNo")
    val lotNo: String?,

    @SerializedName("expirationDate")
    val expirationDate: String?,

    @SerializedName("mfgDate")
    val mfgDate: String?,

    @SerializedName("shelfLifeDays")
    val shelfLifeDays: Double?,

    @SerializedName("sn")
    val sn: String?,

): Serializable