package com.unis.platform.cyclecount.model

import com.unis.platform.location_v2.model.LocationEntity
import com.unis.platform.location_v2.model.LocationType
import java.io.Serializable
import java.util.Date

class CountProcessDataEntity(
    val countTaskEntity: CountTaskEntity,
    var location: LocationEntity? = null,
    var lpId: String? = null,
    var item: ItemEntity? = null,
    var selectedItemUom: ItemUomEntity? = null,
    var itemUomList: List<ItemUomEntity>? = null,
    var itemCountPropertyCompose: ItemCountPropertyCompose? = null,
    var countResults: List<CountResultEntity>? = null,
    private var isOverwrite: Boolean? = false,
    var countInventories: List<CountInventoryEntity>? = null,
//    var curItemHasCountNoMatch: Boolean? = null,
    var countNoMatchItemIdMap: Map<String, List<String>>? = null,
    var recount: Boolean = false,
    var newItemCode: String? = null
) : Serializable {
    val customerId: String? = countTaskEntity.countTicket?.customerId

    data class ItemCountPropertyCompose(
        val qty: Double,
        val lotNo: String? = null,
        val mfgDate: Date? = null,
        val expirationDate: Date? = null,
        val shelfLifeDays: Int? = null,
    )

    fun updateCountResultEntity(countResults: List<CountResultEntity>?) {
        this.countResults = countResults
    }

    fun updateLpId(lpId: String?) {
        this.lpId = lpId
    }

    fun updateLocation(location: LocationEntity?) {
        this.location = location
    }

    fun updateItem(item: ItemEntity) {
        this.item = item
    }

    fun updateItemUom(itemUomList: List<ItemUomEntity>) {
        this.itemUomList = itemUomList
    }

    fun updateSelectedItemUom(selectedItemUom: ItemUomEntity?) {
        this.selectedItemUom = selectedItemUom
    }

    fun updateItemCountPropertyCompose(itemCountPropertyCompose: ItemCountPropertyCompose) {
        this.itemCountPropertyCompose = itemCountPropertyCompose
    }

    fun updateIsOverwrite(isOverwrite: Boolean) {
        this.isOverwrite = isOverwrite
    }

    fun updateCountInventory(countInventories: List<CountInventoryEntity>?) {
        this.countInventories = countInventories
    }

    fun getTaskAllCountLocationId(): List<String> {
        return this.countTaskEntity.taskLines.map { v -> v.locationId }.filter { v -> !v.isNullOrEmpty() }
    }

    fun getRequireCountItemIds(): List<String> {
        return this.countTaskEntity.taskLines.map { v -> v.itemId }.distinct()
    }

    fun isRequireCollectLotNo(): Boolean {
        return item?.requireCollectLotNo ?: false
    }

    fun isRequireCollectExpirationDate(): Boolean {
        return item?.requireCollectExpirationDate ?: false
    }

    fun isRequireCollectMfgDate(): Boolean {
        return item?.requireCollectMfgDate ?: false
    }

    fun isRequireCollectShelfLifeDays(): Boolean {
        return item?.requireCollectShelfLifeDays ?: false
    }

    fun isRequireCollectSN(): Boolean {
        return item?.requireCollectSN ?: false
    }

    fun isCountByLp(): Boolean {
        return countTaskEntity.countTicket?.level == CountTicketLevelEntity.PALLET_COUNT
    }

    fun isOverwrite(): Boolean = isOverwrite == true

    fun isPickLocation(): Boolean = location?.type == LocationType.PICK

    fun isBlind(): Boolean = countTaskEntity.countTicket?.isBlind == true

    fun isCountSpecialItem(): Boolean = countTaskEntity.countTicket?.type == CountTicketTypeEntity.BY_ITEM
            || countTaskEntity.countTicket?.type == CountTicketTypeEntity.BY_ITEM_LOCATION

    fun isCountByItem(): Boolean = countTaskEntity.countTicket?.type == CountTicketTypeEntity.BY_ITEM

    fun getCountMethod() = countTaskEntity.countTicket?.countMethod

    fun putCountNoMatchItemIdMap(countNoMatchItemIdMap: Map<String, List<String>>) {
        this.countNoMatchItemIdMap = countNoMatchItemIdMap
    }

    fun isCountedNoMatchInCurrentLocation(itemId: String?): Boolean {
        return itemId?.let {
            countNoMatchItemIdMap?.get(location?.id)?.contains(it) ?: false
        } ?: false
    }

    fun isCountedNoMatch(itemId: String?, locationId: String?): Boolean {
        locationId?: return false
        return itemId?.let {
            countNoMatchItemIdMap?.get(locationId)?.contains(it) ?: false
        } ?: false
    }

    fun hasReScanLp(): Boolean {
        return lpId?.isNotEmpty() ?: false
    }

    fun isRecount(): Boolean {
        return recount
    }
    
    fun setIsRecount(recount: Boolean) {
        this.recount = recount
    }

    fun clearItem() {
        item = null
        selectedItemUom = null
        itemUomList = null
        itemCountPropertyCompose = null
        isOverwrite = false
        newItemCode = null
        recount = false
    }

    fun clearLpAndItem() {
        lpId = null
        clearItem()
    }
}