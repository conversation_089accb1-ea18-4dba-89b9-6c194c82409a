package com.unis.platform.item.model

import com.google.gson.annotations.SerializedName
import java.io.Serializable

data class ItemEntity(

    @SerializedName("id")
    val id: String,

    @SerializedName("tags")
    val tags: List<ItemTagEntity>,

    @SerializedName("hasSerialNumber")
    val hasSerialNumber: Boolean,

    @SerializedName("serialNoValidationRule")
    val serialNoValidationRule: String?,

    @SerializedName("hasRFID")
    val hasRFID: Boolean,

    @SerializedName("skipSnCollectIfInventoryNoSn")
    val skipSnCollectIfInventoryNoSn: Boolean,

    @SerializedName("requireCollectSnOnShipping")
    val requireCollectSnOnShipping: Boolean,

    @SerializedName("isBom")
    val isBom: Boolean,

    @SerializedName("grade")
    val grade: String,

    @SerializedName("billingGrade")
    val billingGrade: String,

    @SerializedName("freightClass")
    val freightClass: String,

    @SerializedName("countLevel")
    val countLevel: String,

    @SerializedName("nmfc")
    val nmfc: String,

    @SerializedName("commodityCode")
    val commodityCode: String,

    @SerializedName("commodityDescription")
    val commodityDescription: String,

    @SerializedName("serialNoLength")
    val serialNoLength: Int?,

    @SerializedName("validationInboundSerialNo")
    val validationInboundSerialNo: Boolean,

    @SerializedName("serialNoScanLotNoCheck")
    val serialNoScanLotNoCheck: Boolean,

    @SerializedName("validationOutboundSerialNo")
    val validationOutboundSerialNo: Boolean,

    @SerializedName("validatedOutboundSerialNoAgainstInbound")
    val validatedOutboundSerialNoAgainstInbound: Boolean,

    @SerializedName("labels")
    val labels: List<String>,

    @SerializedName("titleIds")
    val titleIds: List<String>?,

    @SerializedName("supplierIds")
    val supplierIds: List<String>?,

    @SerializedName("isHazardousMaterial")
    val isHazardousMaterial: Boolean,

    @SerializedName("countryOrigin")
    val countryOrigin: String,

    @SerializedName("name")
    val name: String,

    @SerializedName("code")
    val code: String,

    @SerializedName("description")
    val description: String?,

    @SerializedName("customerId")
    val customerId: String,

    @SerializedName("status")
    val status: ItemStatusEntity,

    @SerializedName("shortDescription")
    val shortDescription: String?,

    @SerializedName("abbreviation")
    val abbreviation: String,

    @SerializedName("upcCode")
    val upcCode: String?,

    @SerializedName("upcCodeCase")
    val upcCodeCase: String?,

    @SerializedName("eanCode")
    val eanCode: String,

    @SerializedName("requireCollectLotNoOnReceive")
    val requireCollectLotNoOnReceive: Boolean,

    @SerializedName("requireCollectExpirationDateOnReceive")
    val requireCollectExpirationDateOnReceive: Boolean,

    @SerializedName("requireCollectMfgDateOnReceive")
    val requireCollectMfgDateOnReceive: Boolean,

    @SerializedName("requireCollectShelfLifeDaysOnReceive")
    val requireCollectShelfLifeDaysOnReceive: Boolean,

    @SerializedName("requireCollectSnOnReceive")
    val requireCollectSnOnReceive: Boolean,

    @SerializedName("imageFileIds")
    var imageFileIds: List<String>?,

    @SerializedName("groupIds")
    var groupIds: List<Int>?,

    @SerializedName("brandId")
    val brandId: String?,

    @SerializedName("customerName")
    val customerName: String?,

    @SerializedName("titleName")
    val titleName: String?,

    @SerializedName("supplierName")
    val supplierName: String?,

    @SerializedName("stack")
    val stack: String?,

    @SerializedName("shipAllowDays")
    val shipAllowDays: Int?,

    @SerializedName("storageDays")
    val storageDays: Int?,

    @SerializedName("shippingRule")
    val shippingRule: ShippingRuleEntity?
    ):Serializable
