package com.linc.platform.core;

import android.content.Context;
import android.content.SharedPreferences;
import androidx.annotation.NonNull;
import android.text.TextUtils;

import com.annimon.stream.Stream;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.linc.platform.baseapp.model.PickType;
import com.linc.platform.http.TokenInfoEntity;
import com.linc.platform.infoclock.model.InfoClockEmployeeViewEntry;
import com.linc.platform.idm.model.UserViewEntry;
import com.linc.platform.print.model.PrintServerEntry;
import com.linc.platform.receive.v1.model.ReceiveVersionEnum;
import com.linc.platform.utils.CollectionUtil;
import com.linc.platform.utils.Logger;
import com.linc.platform.utils.StringUtil;
import com.unis.platform.iam.model.UserInfoEntity;
import com.unis.platform.iam.model.UserCapability;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.ObjectInputStream;
import java.io.ObjectOutputStream;
import java.io.StringReader;
import java.lang.reflect.Type;
import java.util.Arrays;
import java.util.HashSet;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 */
public class LocalPersistence {
    public static final String SP_NAME = "local_data";
    public static final String SP_KEY_USER_TOKEN = "user_token";
    public static final String SP_KEY_ACCESS_TOKEN = "access_token";
    public static final String SP_KEY_USER_NAME = "user_name";
    public static final String SP_KEY_SERVER_ADDR = "server_addr";
    public static final String SP_KEY_INFO_CLOCK_SERVER_ADDR = "info_clock_server_addr";
    public static final String SP_KEY_USER_ID = "user_id";
    public static final String SP_KEY_COMPANY = "company";
    public static final String SP_KEY_ACCOUNT = "account";
    public static final String SP_KEY_COMPANY_IDS = "company_ids";
    public static final String SP_KEY_IDM_ENTRY = "idm_entry";
    public static final String SP_KEY_IAM_ENTRY = "iam_entry";
    public static final String SP_KEY_EMPLOYEE_ENTRY = "employee_entry";
    public static final String SP_KEY_CUBE_SCAN_SERVER = "cube_scan_server";
    public static final String SP_KEY_CURRENT_VERSION_CODE = "version_code";
    public static final String SP_KEY_LAST_PICKED_LOCATION = "last_picked_location";
    public static final String SP_KEY_LAST_PICKED_LOCATION_ID = "last_picked_location_id";
    public static final String SP_KEY_LAST_PICKED_LOCATION_SQE = "last_picked_location_sqe";
    public static final String SP_KEY_LAST_PICKED_LOCATION_TAG_IDS = "last_picked_location_tag_ids";
    public static final String SP_KEY_LAST_PICKED_LOCATION_SUPPORT_PICK_TYPE = "sp_key_last_picked_location_support_pick_type";
    public static final String SP_KEY_NEW_PICK_STEP = "new_pick_step";
    public static final String SP_KEY_REPLENISHMENT_UOM_DOWNGRADE = "replenishment_uom_downgrade";
    public static final String SP_KEY_INVENTORY_ADJUSTMENT = "inventory_adjustment";
    public static final String SP_KEY_PICK_CURRENT_ROUND_NO = "pick_current_round_no";
    public static final String SP_KEY_PICK_TO_LP_TYPE = "pick_to_lp_type";
    public static final String SP_KEY_RECEIVE_ONE_QTY_SUBMIT = "one_qty_submit";
    public static final String SP_KEY_BACK_END_TRACE_LOG = "back_end_trace_log";
    public static final String SP_KEY_HELP_MANAGEMENT_PAGE_MAP = "help_management_page_map";
    public static final String SP_KEY_CURRENT_LANG = "current_lang";
    public static final String SP_KEY_IGNORE_APPROVAL_UNRATED_TASK_ALERT = "ignore_approval_unrated_tasks_alert";
    public static final String SP_KEY_CUSTOMIZE_DEVICE_ID = "customize_device_id";
    public static final String SP_KEY_IS_NEW_HOME_VERSION = "is_new_home_version";
    public static final String SP_KEY_ENABLE_LOCAL_PRINTER = "enable_local_printer";
    public static final String SP_KEY_IS_REMEMBER_PASSWORD = "is_remember_password";
    public static final String SP_KEY_USER_ACCOUNT = "user_account";
    public static final String SP_KEY_PASSWORD = "password";
    public static final String SP_KEY_PAIRED_BLUETOOTH = "paired_bluetooth";
    public static final String SP_KEY_RELEASE_NOTE_VERSION = "release_note_version";

    public static final String SP_KEY_LAST_MILE_QC_FAVORITE_ROUTES = "last_mile_qc_favorite_routes";

    public static final String SP_KEY_USER_CAPABILITIES = "user_capabilities";


    public static final String SP_KEY_DC_SERVER_ADDR = "dc_server_new_addr";

    public static String getAccount(@NonNull Context context) {
        SharedPreferences sp = context.getSharedPreferences(SP_NAME, Context.MODE_PRIVATE);
        return sp.getString(SP_KEY_ACCOUNT, "");
    }

    public static void saveAccount(@NonNull Context context, String account) {
        SharedPreferences sp = context.getSharedPreferences(SP_NAME, Context.MODE_PRIVATE);
        sp.edit().putString(SP_KEY_ACCOUNT, account).apply();
    }

    public static String getUserName(@NonNull Context context) {
        SharedPreferences sp = context.getSharedPreferences(SP_NAME, Context.MODE_PRIVATE);
        return sp.getString(SP_KEY_USER_NAME, "");
    }

    public static void saveUserName(@NonNull Context context, String userName) {
        SharedPreferences sp = context.getSharedPreferences(SP_NAME, Context.MODE_PRIVATE);
        sp.edit().putString(SP_KEY_USER_NAME, userName).apply();
    }

    public static void clearUserName(@NonNull Context context) {
        SharedPreferences sp = context.getSharedPreferences(SP_NAME, Context.MODE_PRIVATE);
        sp.edit().putString(SP_KEY_USER_NAME, "").apply();
    }
    @Deprecated
    public static String getUserToken(@NonNull Context context) {
        SharedPreferences sp = context.getSharedPreferences(SP_NAME, Context.MODE_PRIVATE);
        return sp.getString(SP_KEY_USER_TOKEN, "");
    }
    @Deprecated
    public static void saveUserToken(@NonNull Context context, String userToken) {
        SharedPreferences sp = context.getSharedPreferences(SP_NAME, Context.MODE_PRIVATE);
        sp.edit().putString(SP_KEY_USER_TOKEN, userToken).apply();
    }
    @Deprecated
    public static void clearUserToken(@NonNull Context context) {
        SharedPreferences sp = context.getSharedPreferences(SP_NAME, Context.MODE_PRIVATE);
        sp.edit().putString(SP_KEY_USER_TOKEN, "").apply();
    }

    public static void saveUserId(@NonNull Context context, String userId) {
        SharedPreferences sp = context.getSharedPreferences(SP_NAME, Context.MODE_PRIVATE);
        sp.edit().putString(SP_KEY_USER_ID, userId).apply();
    }

    public static void clearUserId(@NonNull Context context) {
        SharedPreferences sp = context.getSharedPreferences(SP_NAME, Context.MODE_PRIVATE);
        sp.edit().putString(SP_KEY_USER_ID, "").apply();
    }

    public static String getUserId(@NonNull Context context) {
        SharedPreferences sp = context.getSharedPreferences(SP_NAME, Context.MODE_PRIVATE);
        return sp.getString(SP_KEY_USER_ID, "");
    }

    public static void saveServerAddr(@NonNull Context context, String serverAddr) {
        SharedPreferences sp = context.getSharedPreferences(SP_NAME, Context.MODE_PRIVATE);
        sp.edit().putString(SP_KEY_SERVER_ADDR, serverAddr).apply();
    }

    public static String getServerAddr(@NonNull Context context) {
        SharedPreferences sp = context.getSharedPreferences(SP_NAME, Context.MODE_PRIVATE);
        return sp.getString(SP_KEY_SERVER_ADDR, null);
    }

    public static void saveInfoClockServerAddr(@NonNull Context context, String serverAddr) {
        SharedPreferences sp = context.getSharedPreferences(SP_NAME, Context.MODE_PRIVATE);
        sp.edit().putString(SP_KEY_INFO_CLOCK_SERVER_ADDR, serverAddr).apply();
    }

    public static String getInfoClockServerAddr(@NonNull Context context) {
        SharedPreferences sp = context.getSharedPreferences(SP_NAME, Context.MODE_PRIVATE);
        return sp.getString(SP_KEY_INFO_CLOCK_SERVER_ADDR, null);
    }


    public static void setCompany(@NonNull Context context, String warehouse) {
        SharedPreferences sp = context.getSharedPreferences(SP_NAME, Context.MODE_PRIVATE);
        sp.edit().putString(SP_KEY_COMPANY, warehouse).apply();
    }

    public static String getCompany(@NonNull Context context) {
        SharedPreferences sp = context.getSharedPreferences(SP_NAME, Context.MODE_PRIVATE);
        return sp.getString(SP_KEY_COMPANY, "");
    }

    public static void setCompanyIds(@NonNull Context context, String warehouse) {
        SharedPreferences sp = context.getSharedPreferences(SP_NAME, Context.MODE_PRIVATE);
        sp.edit().putString(SP_KEY_COMPANY_IDS, warehouse).apply();
    }

    public static String getCompanyIds(@NonNull Context context) {
        SharedPreferences sp = context.getSharedPreferences(SP_NAME, Context.MODE_PRIVATE);
        return sp.getString(SP_KEY_COMPANY_IDS, "");
    }

    public static void setCubeScanServer(@NonNull Context context, PrintServerEntry printServer) {
        SharedPreferences sp = context.getSharedPreferences(SP_NAME, Context.MODE_PRIVATE);
        ByteArrayOutputStream bos = new ByteArrayOutputStream();
        ObjectOutputStream os;

        try {
            os = new ObjectOutputStream(bos);
            os.writeObject(printServer);
            String bytesToHexString = StringUtil.bytesToHexString(bos.toByteArray());
            sp.edit().putString(SP_KEY_CUBE_SCAN_SERVER, bytesToHexString).apply();
        } catch (IOException e) {
            Logger.e(e.getMessage());
        }
    }

    public static PrintServerEntry getCubeScanServer(@NonNull Context context) {
        try {
            SharedPreferences sp = context.getSharedPreferences(SP_NAME, Context.MODE_PRIVATE);
            if (sp.contains(SP_KEY_CUBE_SCAN_SERVER)) {
                String cubeScanServerString = sp.getString(SP_KEY_CUBE_SCAN_SERVER, "");
                if (TextUtils.isEmpty(cubeScanServerString)) {
                    return null;
                } else {
                    byte[] stringToBytes = StringUtil.hexStringToBytes(cubeScanServerString);
                    ByteArrayInputStream bis = new ByteArrayInputStream(stringToBytes);
                    ObjectInputStream is = new ObjectInputStream(bis);
                    Object readObject = is.readObject();
                    return (PrintServerEntry) readObject;
                }
            }
        } catch (IOException | ClassNotFoundException e) {
            Logger.e(e.getMessage());
        }

        return null;
    }
    @Deprecated
    public static void setIdmEntry(@NonNull Context context, UserViewEntry idmEntry) {
        SharedPreferences sp = context.getSharedPreferences(SP_NAME, Context.MODE_PRIVATE);
        ByteArrayOutputStream bos = new ByteArrayOutputStream();
        ObjectOutputStream os;

        try {
            os = new ObjectOutputStream(bos);
            os.writeObject(idmEntry);
            String bytesToHexString = StringUtil.bytesToHexString(bos.toByteArray());
            sp.edit().putString(SP_KEY_IDM_ENTRY, bytesToHexString).apply();
        } catch (IOException e) {
            Logger.e(e.getMessage());
        }
    }
    @Deprecated
    public static UserViewEntry getIdmEntry(@NonNull Context context) {
        try {
            SharedPreferences sp = context.getSharedPreferences(SP_NAME, Context.MODE_PRIVATE);
            if (sp.contains(SP_KEY_IDM_ENTRY)) {
                String idmEntryString = sp.getString(SP_KEY_IDM_ENTRY, "");
                if (TextUtils.isEmpty(idmEntryString)) {
                    return null;
                } else {
                    byte[] stringToBytes = StringUtil.hexStringToBytes(idmEntryString);
                    ByteArrayInputStream bis = new ByteArrayInputStream(stringToBytes);
                    ObjectInputStream is = new ObjectInputStream(bis);
                    Object readObject = is.readObject();
                    return (UserViewEntry) readObject;
                }
            }
        } catch (IOException | ClassNotFoundException e) {
            Logger.e(e.getMessage());
        }

        return null;
    }

    public static void setIamEntity(@NonNull Context context, UserInfoEntity iamEntity) {
        SharedPreferences sp = context.getSharedPreferences(SP_NAME, Context.MODE_PRIVATE);
        ByteArrayOutputStream bos = new ByteArrayOutputStream();
        ObjectOutputStream os;

        try {
            os = new ObjectOutputStream(bos);
            os.writeObject(iamEntity);
            String bytesToHexString = StringUtil.bytesToHexString(bos.toByteArray());
            sp.edit().putString(SP_KEY_IAM_ENTRY, bytesToHexString).apply();
        } catch (IOException e) {
            Logger.e(e.getMessage());
        }
    }

    public static UserInfoEntity getIamEntity(@NonNull Context context) {
        try {
            SharedPreferences sp = context.getSharedPreferences(SP_NAME, Context.MODE_PRIVATE);
            if (sp.contains(SP_KEY_IAM_ENTRY)) {
                String iamEntityString = sp.getString(SP_KEY_IAM_ENTRY, "");
                if (TextUtils.isEmpty(iamEntityString)) {
                    return null;
                } else {
                    byte[] stringToBytes = StringUtil.hexStringToBytes(iamEntityString);
                    ByteArrayInputStream bis = new ByteArrayInputStream(stringToBytes);
                    ObjectInputStream is = new ObjectInputStream(bis);
                    Object readObject = is.readObject();
                    return (UserInfoEntity) readObject;
                }
            }
        } catch (IOException | ClassNotFoundException e) {
            Logger.e(e.getMessage());
        }

        return null;
    }

    public static void setTokenInfoEntity(@NonNull Context context, TokenInfoEntity tokenInfoEntity) {
        SharedPreferences sp = context.getSharedPreferences(SP_NAME, Context.MODE_PRIVATE);
        ByteArrayOutputStream bos = new ByteArrayOutputStream();
        ObjectOutputStream os;

        try {
            os = new ObjectOutputStream(bos);
            os.writeObject(tokenInfoEntity);
            String bytesToHexString = StringUtil.bytesToHexString(bos.toByteArray());
            sp.edit().putString(SP_KEY_ACCESS_TOKEN, bytesToHexString).apply();
        } catch (IOException e) {
            Logger.e(e.getMessage());
        }
    }

    public static TokenInfoEntity getTokenInfoEntity(@NonNull Context context) {
        try {
            SharedPreferences sp = context.getSharedPreferences(SP_NAME, Context.MODE_PRIVATE);
            if (sp.contains(SP_KEY_ACCESS_TOKEN)) {
                String iamEntityString = sp.getString(SP_KEY_ACCESS_TOKEN, "");
                if (TextUtils.isEmpty(iamEntityString)) {
                    return null;
                } else {
                    byte[] stringToBytes = StringUtil.hexStringToBytes(iamEntityString);
                    ByteArrayInputStream bis = new ByteArrayInputStream(stringToBytes);
                    ObjectInputStream is = new ObjectInputStream(bis);
                    Object readObject = is.readObject();
                    return (TokenInfoEntity) readObject;
                }
            }
        } catch (IOException | ClassNotFoundException e) {
            Logger.e(e.getMessage());
        }

        return null;
    }

    public static void clearTokenInfoEntity(@NonNull Context context) {
        SharedPreferences sp = context.getSharedPreferences(SP_NAME, Context.MODE_PRIVATE);
        sp.edit().putString(SP_KEY_ACCESS_TOKEN, "").apply();
    }

    public static void setEmployeeInfoEntry(@NonNull Context context, InfoClockEmployeeViewEntry infoClockEmployeeViewEntry) {
        SharedPreferences sp = context.getSharedPreferences(SP_NAME, Context.MODE_PRIVATE);
        ByteArrayOutputStream bos = new ByteArrayOutputStream();
        ObjectOutputStream os;

        try {
            os = new ObjectOutputStream(bos);
            os.writeObject(infoClockEmployeeViewEntry);
            String bytesToHexString = StringUtil.bytesToHexString(bos.toByteArray());
            sp.edit().putString(SP_KEY_EMPLOYEE_ENTRY, bytesToHexString).apply();
        } catch (IOException e) {
            Logger.e(e.getMessage());
        }
    }

    public static InfoClockEmployeeViewEntry getEmployeeInfoEntry(@NonNull Context context) {
        try {
            SharedPreferences sp = context.getSharedPreferences(SP_NAME, Context.MODE_PRIVATE);
            if (sp.contains(SP_KEY_EMPLOYEE_ENTRY)) {
                String idmEntryString = sp.getString(SP_KEY_EMPLOYEE_ENTRY, "");
                if (TextUtils.isEmpty(idmEntryString)) {
                    return null;
                } else {
                    byte[] stringToBytes = StringUtil.hexStringToBytes(idmEntryString);
                    ByteArrayInputStream bis = new ByteArrayInputStream(stringToBytes);
                    ObjectInputStream is = new ObjectInputStream(bis);
                    Object readObject = is.readObject();
                    return (InfoClockEmployeeViewEntry) readObject;
                }
            }
        } catch (IOException | ClassNotFoundException e) {
            Logger.e(e.getMessage());
        }

        return null;
    }

    public static void setCurrentVersionCode(@NonNull Context context, int versionCode) {
        SharedPreferences sp = context.getSharedPreferences(SP_NAME, Context.MODE_PRIVATE);
        sp.edit().putInt(SP_KEY_CURRENT_VERSION_CODE, versionCode).apply();
    }

    public static int getCurrentVersionCode(@NonNull Context context) {
        SharedPreferences sp = context.getSharedPreferences(SP_NAME, Context.MODE_PRIVATE);
        return sp.getInt(SP_KEY_CURRENT_VERSION_CODE, 0);
    }

    public static void saveCurrentPickRoundNo(@NonNull Context context, String taskId, int currentRoundNo) {
        SharedPreferences sp = context.getSharedPreferences(SP_KEY_PICK_CURRENT_ROUND_NO, Context.MODE_PRIVATE);
        sp.edit().putInt(taskId, currentRoundNo).apply();
    }

    public static int getCurrentPickRoundNo(@NonNull Context context, String taskId) {
        try {
            SharedPreferences sp = context.getSharedPreferences(SP_KEY_PICK_CURRENT_ROUND_NO, Context.MODE_PRIVATE);
            return sp.getInt(taskId, 1);
        } catch (Exception e) {
            return 1;
        }
    }

    public static void clearPickRoundNo(@NonNull Context context, String taskId) {
        SharedPreferences sp = context.getSharedPreferences(SP_KEY_PICK_CURRENT_ROUND_NO, Context.MODE_PRIVATE);
        sp.edit().putString(taskId, "").apply();
    }

    public static String getLastPickLocation(@NonNull Context context) {
        SharedPreferences sp = context.getSharedPreferences(SP_NAME, Context.MODE_PRIVATE);
        return sp.getString(SP_KEY_LAST_PICKED_LOCATION, "");
    }

    public static void saveLastPickLocation(@NonNull Context context, String location) {
        SharedPreferences sp = context.getSharedPreferences(SP_NAME, Context.MODE_PRIVATE);
        sp.edit().putString(SP_KEY_LAST_PICKED_LOCATION, location).apply();
    }

    public static Long getLastPickLocationSqe(@NonNull Context context) {
        SharedPreferences sp = context.getSharedPreferences(SP_NAME, Context.MODE_PRIVATE);
        return sp.getLong(SP_KEY_LAST_PICKED_LOCATION_SQE, 0L);
    }

    public static void saveLastPickLocationSqe(@NonNull Context context, long sequence) {
        SharedPreferences sp = context.getSharedPreferences(SP_NAME, Context.MODE_PRIVATE);
        sp.edit().putLong(SP_KEY_LAST_PICKED_LOCATION_SQE, sequence).apply();
    }

    public static boolean isReceiveOneQtySubmit(@NonNull Context context) {
        SharedPreferences sp = context.getSharedPreferences(SP_NAME, Context.MODE_PRIVATE);
        return sp.getBoolean(SP_KEY_RECEIVE_ONE_QTY_SUBMIT, false);
    }

    public static void setReceiveOneQtySubmit(@NonNull Context context, boolean isReceiveOneQtySubmit) {
        SharedPreferences sp = context.getSharedPreferences(SP_NAME, Context.MODE_PRIVATE);
        sp.edit().putBoolean(SP_KEY_RECEIVE_ONE_QTY_SUBMIT, isReceiveOneQtySubmit).apply();
    }

    public static boolean isNeedBackEndTrackLog(@NonNull Context context) {
        SharedPreferences sp = context.getSharedPreferences(SP_NAME, Context.MODE_PRIVATE);
        return sp.getBoolean(SP_KEY_BACK_END_TRACE_LOG, false);
    }

    public static void setBackEndTrackLog(@NonNull Context context, boolean isNeed) {
        SharedPreferences sp = context.getSharedPreferences(SP_NAME, Context.MODE_PRIVATE);
        sp.edit().putBoolean(SP_KEY_BACK_END_TRACE_LOG, isNeed).apply();
    }

    public static void setBoolean(@NonNull Context context, String key, boolean value) {
        SharedPreferences sp = context.getSharedPreferences(SP_NAME, Context.MODE_PRIVATE);
        sp.edit().putBoolean(key, value).apply();
    }

    public static boolean getBoolean(@NonNull Context context, String key, boolean defaultValue) {
        SharedPreferences sp = context.getSharedPreferences(SP_NAME, Context.MODE_PRIVATE);
        return sp.getBoolean(key, defaultValue);
    }

    public static Set<String> getLincCookies(@NonNull Context context) {
        SharedPreferences sp = context.getSharedPreferences(SP_NAME, Context.MODE_PRIVATE);
        return sp.getStringSet("Cookie", new HashSet<>());
    }

    public static void setLincCookies(@NonNull Context context, HashSet<String> cookies) {
        SharedPreferences sp = context.getSharedPreferences(SP_NAME, Context.MODE_PRIVATE);
        sp.edit().putStringSet("Cookie", cookies).apply();
    }

    public static String getPickToLPType(@NonNull Context context) {
        SharedPreferences sp = context.getSharedPreferences(SP_NAME, Context.MODE_PRIVATE);
        return sp.getString(SP_KEY_PICK_TO_LP_TYPE, "CLP");
    }

    public static void setPickToLPType(@NonNull Context context, String pickToLpType) {
        SharedPreferences sp = context.getSharedPreferences(SP_NAME, Context.MODE_PRIVATE);
        sp.edit().putString(SP_KEY_PICK_TO_LP_TYPE, pickToLpType).apply();
    }

    public static void setLastPickedLocationId(Context context, String locationId) {
        SharedPreferences sp = context.getSharedPreferences(SP_NAME, Context.MODE_PRIVATE);
        sp.edit().putString(SP_KEY_LAST_PICKED_LOCATION_ID, locationId).apply();
    }

    public static String getLastPickedLocationId(Context context) {
        SharedPreferences sp = context.getSharedPreferences(SP_NAME, Context.MODE_PRIVATE);
        return sp.getString(SP_KEY_LAST_PICKED_LOCATION_ID, "");
    }

    public static void saveLastPickLocationTagIds(Context context, List<String> tagIds) {
        SharedPreferences sp = context.getSharedPreferences(SP_NAME, Context.MODE_PRIVATE);
        if (CollectionUtil.isNullOrEmpty(tagIds)) {
            sp.edit().putString(SP_KEY_LAST_PICKED_LOCATION_TAG_IDS, "").apply();
        }
        try {
            Gson gson = new Gson();
            String idsStr = gson.toJson(tagIds);
            sp.edit().putString(SP_KEY_LAST_PICKED_LOCATION_TAG_IDS, idsStr).apply();
        } catch (Exception e) {
            Logger.e(e.getMessage());
        }

    }

    public static List<String> getLastPickLocationTagIds(Context context) {
        SharedPreferences sp = context.getSharedPreferences(SP_NAME, Context.MODE_PRIVATE);
        String idsStr = sp.getString(SP_KEY_LAST_PICKED_LOCATION_TAG_IDS, "");
        if (idsStr.isEmpty()) return null;
        Gson gson = new Gson();
        StringReader reader = new StringReader(idsStr);
        return gson.fromJson(reader, new TypeToken<List<String>>() {
        }.getType());
    }

    public static void saveLastPickLocationSupportPickType(Context context, PickType supportPickType) {
        SharedPreferences sp = context.getSharedPreferences(SP_NAME, Context.MODE_PRIVATE);
        if (supportPickType != null) {
            try {
                Gson gson = new Gson();
                String supportPickTypeJson = gson.toJson(supportPickType);
                sp.edit().putString(SP_KEY_LAST_PICKED_LOCATION_SUPPORT_PICK_TYPE, supportPickTypeJson).apply();
            } catch (Exception e) {
                Logger.e(e.getMessage());
            }
        } else {
            sp.edit().putString(SP_KEY_LAST_PICKED_LOCATION_SUPPORT_PICK_TYPE, "").apply();
        }
    }

    public static PickType getLastPickLocationSupportPickType(Context context) {
        SharedPreferences sp = context.getSharedPreferences(SP_NAME, Context.MODE_PRIVATE);
        String supportPickTypeJson = sp.getString(SP_KEY_LAST_PICKED_LOCATION_SUPPORT_PICK_TYPE, "");
        if (supportPickTypeJson.isEmpty()) return null;
        Gson gson = new Gson();
        StringReader reader = new StringReader(supportPickTypeJson);
        return gson.fromJson(reader, PickType.class);
    }

    public static void saveCurrentLang(Context context, String lang) {
        SharedPreferences sp = context.getSharedPreferences(SP_NAME, Context.MODE_PRIVATE);
        sp.edit().putString(SP_KEY_CURRENT_LANG, lang).apply();
    }

    public static String getCurrentLang(Context context) {
        SharedPreferences sp = context.getSharedPreferences(SP_NAME, Context.MODE_PRIVATE);
        return sp.getString(SP_KEY_CURRENT_LANG, "");
    }

    public static void removeCurrentLang(Context context) {
        SharedPreferences sp = context.getSharedPreferences(SP_NAME, Context.MODE_PRIVATE);
        sp.edit().remove(SP_KEY_CURRENT_LANG);
    }


    public static void saveRegistrationId(Context context, String userId, String registrationId) {
        SharedPreferences sp = context.getSharedPreferences(SP_NAME, Context.MODE_PRIVATE);
        sp.edit().putString(userId, registrationId).apply();
    }

    public static String getRegistrationId(Context context, String userId) {
        SharedPreferences sp = context.getSharedPreferences(SP_NAME, Context.MODE_PRIVATE);
        return sp.getString(userId, "");
    }

    public static void removeRegistrationId(@NonNull Context context, String userId) {
        SharedPreferences sp = context.getSharedPreferences(SP_NAME, Context.MODE_PRIVATE);
        sp.edit().remove(userId).apply();
    }

    public static void setString(@NonNull Context context, String key, String value) {
        SharedPreferences sp = context.getSharedPreferences(SP_NAME, Context.MODE_PRIVATE);
        sp.edit().putString(key, value).apply();
    }

    public static String getString(@NonNull Context context, String key) {
        SharedPreferences sp = context.getSharedPreferences(SP_NAME, Context.MODE_PRIVATE);
        return sp.getString(key, "");
    }


    public static void setHomeVersion(@NonNull Context context, ReceiveVersionEnum value) {
        SharedPreferences sp = context.getSharedPreferences(SP_NAME, Context.MODE_PRIVATE);
        Gson gson = new Gson();
        String homeVersion = gson.toJson(value);
        sp.edit().putString(SP_KEY_IS_NEW_HOME_VERSION, homeVersion).apply();
    }

    public static ReceiveVersionEnum getNewHomeVersion(@NonNull Context context) {
        SharedPreferences sp = context.getSharedPreferences(SP_NAME, Context.MODE_PRIVATE);
        String homeVersion = sp.getString(SP_KEY_IS_NEW_HOME_VERSION, ReceiveVersionEnum.OLD.name());
        Gson gson = new Gson();
        StringReader reader = new StringReader(homeVersion);
        return gson.fromJson(reader, ReceiveVersionEnum.class);
    }

    public static void setEnableLocalPrinter(@NonNull Context context, boolean isEnable) {
        SharedPreferences sp = context.getSharedPreferences(SP_NAME, Context.MODE_PRIVATE);
        sp.edit().putBoolean(SP_KEY_ENABLE_LOCAL_PRINTER, isEnable).apply();
    }

    public static boolean isLocalPrinterEnabled(@NonNull Context context) {
        SharedPreferences sp = context.getSharedPreferences(SP_NAME, Context.MODE_PRIVATE);
        return sp.getBoolean(SP_KEY_ENABLE_LOCAL_PRINTER, false);
    }

    public static void saveIsRememberPassword(@NonNull Context context, boolean value) {
        SharedPreferences sp = context.getSharedPreferences(SP_NAME, Context.MODE_PRIVATE);
        String key = SP_KEY_IS_REMEMBER_PASSWORD + getServerAddr(context);
        sp.edit().putBoolean(key, value).apply();
    }

    public static boolean getIsRememberPassword(@NonNull Context context) {
        SharedPreferences sp = context.getSharedPreferences(SP_NAME, Context.MODE_PRIVATE);
        String key = SP_KEY_IS_REMEMBER_PASSWORD + getServerAddr(context);
        return sp.getBoolean(key, false);
    }

    public static void saveUserAccount(@NonNull Context context, String value) {
        SharedPreferences sp = context.getSharedPreferences(SP_NAME, Context.MODE_PRIVATE);
        String key = SP_KEY_USER_ACCOUNT + getServerAddr(context);
        sp.edit().putString(key, value).apply();
    }

    public static String getUserAccount(@NonNull Context context) {
        SharedPreferences sp = context.getSharedPreferences(SP_NAME, Context.MODE_PRIVATE);
        String key = SP_KEY_USER_ACCOUNT + getServerAddr(context);
        return sp.getString(key, "");
    }

    public static void savePassword(@NonNull Context context, String value) {
        SharedPreferences sp = context.getSharedPreferences(SP_NAME, Context.MODE_PRIVATE);
        String key = SP_KEY_PASSWORD + getServerAddr(context);
        sp.edit().putString(key, value).apply();
    }

    public static String getPassword(@NonNull Context context) {
        SharedPreferences sp = context.getSharedPreferences(SP_NAME, Context.MODE_PRIVATE);
        String key = SP_KEY_PASSWORD + getServerAddr(context);
        return sp.getString(key, "");
    }

    public static void saveReleaseNoteVersion(@NonNull Context context, String version) {
        SharedPreferences sp = context.getSharedPreferences(SP_NAME, Context.MODE_PRIVATE);
        String key = SP_KEY_RELEASE_NOTE_VERSION;
        sp.edit().putString(key, version).apply();
    }

    public static String getReleaseNoteVersion(@NonNull Context context) {
        SharedPreferences sp = context.getSharedPreferences(SP_NAME, Context.MODE_PRIVATE);
        String key = SP_KEY_RELEASE_NOTE_VERSION;
        return sp.getString(key, "");
    }

    public static void saveLastMileQcFavoriteRoutes(@NonNull Context context, String userId, Set<String> routeIds) {
        SharedPreferences sp = context.getSharedPreferences(SP_NAME, Context.MODE_PRIVATE);
        String key = SP_KEY_LAST_MILE_QC_FAVORITE_ROUTES + userId;
        String value = Stream.ofNullable(routeIds).reduce((s1, s2) -> s1 + "," + s2).orElse("");
        sp.edit().putString(key,value).apply();
    }

    public static Set<String> getLastMileQcFavoriteRoutes(@NonNull Context context, String userId){
        SharedPreferences sp = context.getSharedPreferences(SP_NAME, Context.MODE_PRIVATE);
        String key = SP_KEY_LAST_MILE_QC_FAVORITE_ROUTES + userId;
        String value = sp.getString(key, "");
        return new LinkedHashSet<>(Arrays.asList(value.split(",")));
    }

    public static void savePairedBluetoothAddress(@NonNull Context context, String address) {
        SharedPreferences sp = context.getSharedPreferences(SP_NAME, Context.MODE_PRIVATE);
        String key = SP_KEY_PAIRED_BLUETOOTH;
        sp.edit().putString(SP_KEY_PAIRED_BLUETOOTH, address).apply();
    }

    public static String getPairedBluetoothAddress(@NonNull Context context) {
        SharedPreferences sp = context.getSharedPreferences(SP_NAME, Context.MODE_PRIVATE);
        return sp.getString(SP_KEY_PAIRED_BLUETOOTH, "");
    }

    /**
     * 保存用户能力列表
     * @param context 上下文
     * @param userId 用户ID
     * @param capabilities 用户能力列表
     */
    public static void saveUserCapabilities(@NonNull Context context, String userId, List<UserCapability> capabilities) {
        SharedPreferences sp = context.getSharedPreferences(SP_NAME, Context.MODE_PRIVATE);
        String key = SP_KEY_USER_CAPABILITIES + "_" + userId;
        if (CollectionUtil.isNullOrEmpty(capabilities)) {
            sp.edit().putString(key, "").apply();
            return;
        }
        try {
            Gson gson = new Gson();
            String capabilitiesJson = gson.toJson(capabilities);
            sp.edit().putString(key, capabilitiesJson).apply();
        } catch (Exception e) {
            Logger.e(e.getMessage());
        }
    }

    /**
     * 获取用户能力列表
     * @param context 上下文
     * @param userId 用户ID
     * @return 用户能力列表，如果未设置则返回null
     */
    public static List<UserCapability> getUserCapabilities(@NonNull Context context, String userId) {
        SharedPreferences sp = context.getSharedPreferences(SP_NAME, Context.MODE_PRIVATE);
        String key = SP_KEY_USER_CAPABILITIES + "_" + userId;
        String capabilitiesJson = sp.getString(key, "");
        if (capabilitiesJson.isEmpty()) {
            return null;
        }
        try {
            Gson gson = new Gson();
            Type type = new TypeToken<List<UserCapability>>(){}.getType();
            return gson.fromJson(capabilitiesJson, type);
        } catch (Exception e) {
            Logger.e(e.getMessage());
            return null;
        }
    }

    /**
     * 清除用户能力列表
     * @param context 上下文
     * @param userId 用户ID
     */
    public static void clearUserCapabilities(@NonNull Context context, String userId) {
        SharedPreferences sp = context.getSharedPreferences(SP_NAME, Context.MODE_PRIVATE);
        String key = SP_KEY_USER_CAPABILITIES + "_" + userId;
        sp.edit().remove(key).apply();
    }


    public static void saveDCServerAddr(@NonNull Context context, String serverAddr) {
        SharedPreferences sp = context.getSharedPreferences(SP_NAME, Context.MODE_PRIVATE);
        sp.edit().putString(SP_KEY_DC_SERVER_ADDR, serverAddr).apply();
    }

    public static String getDCServerAddr(@NonNull Context context) {
        SharedPreferences sp = context.getSharedPreferences(SP_NAME, Context.MODE_PRIVATE);
        return sp.getString(SP_KEY_DC_SERVER_ADDR, null);
    }
}
