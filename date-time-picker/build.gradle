apply from: file("${rootDir}/config/library.gradle")
apply plugin: 'kotlin-android'

android {
    namespace "com.loper7.date_time_picker"

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }

    kotlinOptions {
        jvmTarget = "1.8"
    }
}

dependencies {
    implementation 'androidx.core:core:1.0.0'
    implementation rootProject.ext.dependencies["design"]
    implementation rootProject.ext.dependencies["kotlin-stdlib"]
}

// 创建自定义任务，将生成的 aar 文件复制到 libs 目录
task copyAarToLibs(type: Copy) {
    from "$buildDir/outputs/aar/"
    into "$rootDir/libs/"
    include "*.aar"

    doLast {
        println "AAR files copied to $rootDir/libs/"
    }
}

// 让 assembleRelease 任务完成后自动执行复制任务
afterEvaluate {
    tasks.named('assembleRelease').configure {
        finalizedBy copyAarToLibs
    }
}
