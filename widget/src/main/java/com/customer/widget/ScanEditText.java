package com.customer.widget;

import android.content.Context;
import android.content.res.TypedArray;
import android.graphics.PorterDuff;
import android.graphics.drawable.Drawable;
import androidx.annotation.StringRes;
import com.google.android.material.textfield.TextInputEditText;
import com.google.android.material.textfield.TextInputLayout;
import android.text.Editable;
import android.text.InputType;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.text.method.KeyListener;
import android.util.AttributeSet;
import android.view.KeyEvent;
import android.view.MotionEvent;
import android.view.View;
import android.view.inputmethod.EditorInfo;
import android.view.inputmethod.InputMethodManager;
import android.widget.CheckBox;
import android.widget.TextView;

import com.customer.widget.core.LincBaseActivity;
import com.customer.widget.scanner.decoder.DriverLicense;
import com.customer.widget.util.CommUtil;
import com.linc.platform.common.scanner.LabelField;
import com.linc.platform.utils.CollectionUtil;
import com.linc.platform.utils.Logger;
import com.linc.platform.utils.RxUtil;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;

import io.reactivex.rxjava3.core.Observable;
import io.reactivex.rxjava3.android.schedulers.AndroidSchedulers;
import io.reactivex.rxjava3.core.ObservableOnSubscribe;
import io.reactivex.rxjava3.disposables.Disposable;
import io.reactivex.rxjava3.schedulers.Schedulers;

/**
 * <AUTHOR>
 */
public class ScanEditText extends AbstractScanner implements TextWatcher {
    public static final int MODEL_DARK = 0;
    public static final int MODE_SCAN = 1;
    public static final int MODE_SEARCH = 2;

    private static final long DEFAULT_SEARCH_TRIGGER_TIME = 800;
    // timer trigger seconds
    private static final int TIMER_TIME = 300;

    public TextInputEditText contentEdt;

    protected CheckBox actionChk;

    TextInputLayout contentEdtLayout;

    private OnActionListener onListener;
    private boolean inAddMode = true;
    private boolean inAsycScanMode = false;
    private Drawable clearDrawable;
    private Drawable barcodeDrawable;
    private Drawable searchDrawable;
    private Drawable modeDrawable;
    private boolean hasText = false;
    private InputMethodManager inputMethodManager;
    private int addColor;
    private int removeColor;
    private long lastInputTime;
    private Disposable disposable;
    private long searchTriggerTime = DEFAULT_SEARCH_TRIGGER_TIME;
    private ScanBarcodeDialog scanBarcodeDialog;
    private boolean isShowResult = false;
    private boolean isRescan = false;
    private boolean isLocalQuery = false;
    private Context activityContext;
    private Context context;
    private String temporaryInputStr = "";
    private List<String> snPendingList = new ArrayList<>();
    private long ASYC_SLEEP_TIME = 10;

    private OnTextChangedListener mChangedListener;

    public void setOnTextChangedListener(OnTextChangedListener listener) {
        this.mChangedListener = listener;
    }

    public ScanEditText(Context context) {
        super(context);
        init(context, null);
    }

    public ScanEditText(Context context, AttributeSet attrs) {
        super(context, attrs);
        init(context, attrs);
    }

    public ScanEditText(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init(context, attrs);
    }

    @Override
    protected void setInputRestriction(boolean isRestrict) {
        if (isRestrict) {
            contentEdt.setInputType(InputType.TYPE_NULL);
            contentEdt.setOnClickListener(v -> CommUtil.hideKeyBoard(contentEdt));
        }
    }

    public void setActivityContext(Context context) {
        activityContext = context;
    }

    public void showResult(boolean show) {
        isShowResult = show;
        if (scanBarcodeDialog != null) {
            scanBarcodeDialog.setShowResult(isShowResult);
        }
    }

    public void setBarcodeFormat(int format) {
        if (scanBarcodeDialog != null) {
            scanBarcodeDialog.setBarCodeFormat(format);
        }
    }

    public void setInAsycScanMode(boolean inAsycScanMode) {
        this.inAsycScanMode = inAsycScanMode;
    }

    public void setRescan(boolean isRescan) {
        this.isRescan = isRescan;
        if (scanBarcodeDialog != null) {
            scanBarcodeDialog.setRescan(isRescan);
        }
    }

    public void setScannerViewText(String scannerViewText) {
        if (scanBarcodeDialog != null) {
            scanBarcodeDialog.setScannerViewText(scannerViewText);
        }
    }

    public void setOnActionListener(OnActionListener onListener) {
        this.onListener = onListener;
    }

    public void setLocalQuery(boolean isLocalQuery) {
        this.isLocalQuery = isLocalQuery;
    }

    public void setContentEdtFocusImmediately() {
        contentEdt.setFocusable(true);
        contentEdt.setFocusableInTouchMode(true);
        contentEdt.requestFocus();
    }

    public void setContentEdtFocus() {
        setContentEdtFocus(300);
    }

    public void setContentEdtImeOptions(int imeOptions) {
        contentEdt.setImeOptions(imeOptions);
    }

    public void setContentEdtFocus(long delay) {
        contentEdt.setFocusable(true);
        contentEdt.setFocusableInTouchMode(true);
        Observable.just("").delay(delay, TimeUnit.MILLISECONDS)
                .subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(s -> contentEdt.requestFocus());
    }

    public void setHintText(String hintText) {
        contentEdtLayout.setHint(hintText);
    }

    public void setHintText(@StringRes int resId) {
        contentEdtLayout.setHint(getResources().getString(resId));
    }

    public void clearText() {
        Observable.timer(300L, TimeUnit.MILLISECONDS)
                .compose(RxUtil.asyncSchedulers())
                .subscribe(aLong -> contentEdt.setText(""));
    }

    public void resetEdt() {
        clearText();
        setContentEdtFocus();
    }

    public void setEnable(boolean enable) {
        this.setEnabled(enable);
        contentEdt.setEnabled(enable);
        actionChk.setEnabled(enable);
        updateRightIcon();
    }

    public void setInputTypeToNumber() {
        contentEdt.setInputType(EditorInfo.TYPE_CLASS_NUMBER);
    }

    public void setInputType(int type) {
        contentEdt.setInputType(type);
    }

    public void setSearchTriggerTime(int seconds) {
        searchTriggerTime = seconds * 1000;
    }

    public void showActionButton(boolean show) {
        if (show) {
            actionChk.setVisibility(VISIBLE);
        } else {
            actionChk.setVisibility(GONE);
        }
    }

    public void setOperateMode(int mode) {
        switch (mode) {
            case MODE_SCAN:
                modeDrawable = barcodeDrawable;
                break;
            case MODE_SEARCH:
                modeDrawable = searchDrawable;
                break;
            default:
                Logger.d("nothing to do");
        }
        updateRightIcon();
    }

    public String getText() {
        return contentEdt.getText().toString();
    }

    public void setText(String text) {
        contentEdt.setText(text);
    }

    public void setText(@StringRes int resId) {
        contentEdt.setText(resId);
    }

    public void setError(CharSequence message) {
        contentEdt.setError(message);
    }

    public void addTextChangedListener(TextWatcher textWatcher) {
        contentEdt.addTextChangedListener(textWatcher);
    }

    public void setOnEditorActionListener(TextView.OnEditorActionListener listener) {
        contentEdt.setOnEditorActionListener(listener);
    }

    public boolean isInAddMode() {
        return inAddMode;
    }

    public void doTimer() {
        if (disposable == null) {
            disposable = Observable.just("do timer")
                    .subscribeOn(Schedulers.io())
                    .observeOn(AndroidSchedulers.mainThread())
                    .subscribe((along) -> {
                        try {
                            Thread.sleep(TIMER_TIME);
                        } catch (InterruptedException e) {
                            Logger.e(e.getMessage());
                        }
                        doSearch();
                    });
        }
    }

    public void cancelTimer() {
        if (disposable != null && !disposable.isDisposed()) {
            disposable.dispose();
            disposable = null;
        }
    }

    private void init(Context context, AttributeSet attrs) {
        TypedArray ta = null;
        int model = 0;
        if (attrs != null) {
            ta = getContext().obtainStyledAttributes(attrs, R.styleable.ScanEditText);
            model = ta.getInteger(R.styleable.ScanEditText_styleModel, 0);
        }
        inflate(context, model == MODEL_DARK? R.layout.widget_search_edit : R.layout.widget_search_edit_v1, this);
        this.context = context;
        contentEdt = (TextInputEditText) findViewById(R.id.content_edt);
        actionChk = (CheckBox) findViewById(R.id.action_chk);
        contentEdtLayout = (TextInputLayout) findViewById(R.id.content_edt_layout);
        if (ta != null) {
            ta.recycle();
        }

        actionChk.setOnCheckedChangeListener((buttonView, isChecked) -> {
            inAddMode = isChecked;
            updateLineColor();
        });

        contentEdt.addTextChangedListener(this);

        contentEdt.setOnTouchListener((view, event) -> {
            if (event.getAction() == MotionEvent.ACTION_UP) {
                Drawable drawable = contentEdt.getCompoundDrawables()[2];
                if (drawable != null
                        && (event.getRawX() >= (this.getRight() - drawable.getBounds().width()))) {
                    if (drawable == clearDrawable) {
                        contentEdt.setText("");
                        if (onListener != null) {
                            onListener.onClearText();
                        }
                    } else if (drawable == barcodeDrawable) {
                        if (activityContext != null) {
                            scanBarcodeDialog.show(((LincBaseActivity) activityContext)
                                    .getSupportFragmentManager(), "barcode");
                        } else {
                            scanBarcodeDialog.show(((LincBaseActivity) context)
                                    .getSupportFragmentManager(), "barcode");
                        }
                    }
                    cancelTimer();
                }
            }
            return false;
        });

        contentEdt.setOnKeyListener((View v, int keyCode, KeyEvent event) -> {
            if ((event.getAction() == KeyEvent.ACTION_DOWN)
                    && (keyCode == KeyEvent.KEYCODE_ENTER)) {
                onEnterKey(300L);
                cancelTimer();
                return true;
            } else if (inAsycScanMode && event.getUnicodeChar() > 0) {
                Character barcode = (char) event.getUnicodeChar();
                temporaryInputStr += barcode;
            }
            return false;
        });

        contentEdt.setOnEditorActionListener((textView, actionId, event) -> {
            switch (actionId) {
                case EditorInfo.IME_ACTION_DONE:
                    onEnterKey();
                    cancelTimer();
                    break;
                case EditorInfo.IME_ACTION_NONE:
                    onEnterKey();
                    cancelTimer();
                    break;
                default:
                    break;
            }
            return false;
        });

        scanBarcodeDialog = new ScanBarcodeDialog();
        scanBarcodeDialog.setShowResult(isShowResult);
        scanBarcodeDialog.setRescan(isRescan);
        scanBarcodeDialog.setOnBarcodeResult(new ScanBarcodeDialog.OnBarcodeResult() {
            @Override
            public void onBarcode(String barcode) {
                Observable.just(barcode).delay(500, TimeUnit.MILLISECONDS)
                        .subscribeOn(Schedulers.io())
                        .observeOn(AndroidSchedulers.mainThread())
                        .subscribe(result -> {
                            contentEdt.setText(result);
                            contentEdt.setFocusable(true);
                            contentEdt.setFocusableInTouchMode(true);
                            KeyEvent event = new KeyEvent(KeyEvent.ACTION_DOWN, KeyEvent.KEYCODE_ENTER);
                            contentEdt.dispatchKeyEvent(event);
                        });
            }

            @Override
            public void onDriverLicense(DriverLicense driverLicense) {
                if (onListener != null) {
                    onListener.onAddScanDone(driverLicense.toJson());
                }
            }

            @Override
            public void onDismiss() {

            }
        });

        barcodeDrawable = context.getResources().getDrawable(model == MODEL_DARK? R.drawable.ic_barcode : R.drawable.ic_barcode_blue);
        clearDrawable = context.getResources().getDrawable(model == MODEL_DARK? R.drawable.ic_clear_black : R.drawable.ic_clear_white);
        searchDrawable = context.getResources().getDrawable(model == MODEL_DARK? R.drawable.ic_search_black : R.drawable.ic_search_white);
        modeDrawable = barcodeDrawable;
        addColor = getResources().getColor(model == MODEL_DARK? R.color.colorAccent: R.color.colorAccent);
        removeColor = getResources().getColor(R.color.red);
        inputMethodManager = (InputMethodManager) context.
                getSystemService(Context.INPUT_METHOD_SERVICE);
        updateRightIcon();
    }

    @Override
    public void beforeTextChanged(CharSequence s, int start, int count, int after) {

    }

    @Override
    public void onTextChanged(CharSequence s, int start, int before, int count) {
        if (mChangedListener != null) {
            mChangedListener.onTextChanged(s.toString());
        }
    }

    @Override
    public void afterTextChanged(Editable s) {
        if (inAsycScanMode) return;
        hasText = !TextUtils.isEmpty(s.toString());
        updateRightIcon();
        lastInputTime = System.currentTimeMillis();

        if (isLocalQuery && onListener != null) {
            onListener.onSearch(contentEdt.getText().toString());
        } else if (!hasText) {
            doTimer();
        }
    }

    private void updateRightIcon() {
        if (contentEdt == null) {
            return;
        }
        Drawable[] ds = contentEdt.getCompoundDrawables();
        Drawable drawable;

        if (hasText) {
            drawable = clearDrawable;
        } else {
            drawable = modeDrawable;
        }

        if (!contentEdt.isEnabled()) {
            drawable = ds[0];
        }

        contentEdt.setCompoundDrawablesWithIntrinsicBounds(ds[0], ds[1], drawable, ds[3]);
    }

    private void updateLineColor() {
        int color;

        if (inAddMode) {
            color = addColor;
        } else {
            color = removeColor;
        }

        Drawable drawable = contentEdt.getBackground();
        drawable.setColorFilter(color, PorterDuff.Mode.SRC_ATOP);

        contentEdt.setBackgroundDrawable(drawable);
    }

    private void doSearch() {
        if (System.currentTimeMillis() > (lastInputTime + searchTriggerTime)
                && onListener != null) {
            onListener.onSearch(contentEdt.getText().toString());
            cancelTimer();
        }
    }

    private void onEnterKey(long delay) {
        Observable.timer(delay, TimeUnit.MILLISECONDS)
                .compose(RxUtil.asyncSchedulers())
                .subscribe(aLong -> onEnterKey());
    }

    private void onEnterKey() {
        if (inAsycScanMode && onListener != null) {
            Observable.create((ObservableOnSubscribe<String>) subscriber -> {
                String pendingStr = TextUtils.isEmpty(temporaryInputStr)
                        ? contentEdt.getText().toString()
                        : temporaryInputStr;
                snPendingList.add(pendingStr);
                temporaryInputStr = "";
                subscriber.onNext("");
            }).subscribeOn(Schedulers.io())
                    .observeOn(AndroidSchedulers.mainThread())
                    .subscribe((String) -> {
                        if (CollectionUtil.isNotNullOrEmpty(snPendingList)) {
                            onListener.onAddScanDone(snPendingList.get(0));
                            snPendingList.remove(0);
                        }
                    });
        } else if (inAddMode && onListener != null) {
            onListener.onAddScanDone(contentEdt.getText().toString());
        } else if (!inAddMode && onListener != null) {
            onListener.onRemoveScanDone(contentEdt.getText().toString());
        }
        temporaryInputStr = "";

    }

    public void setKeyListener(KeyListener instance) {
        contentEdt.setKeyListener(instance);
    }

    public void setLabelField(LabelField labelField){
        scanBarcodeDialog.setLabelField(labelField);
    }

    public interface OnActionListener {
        void onAddScanDone(String data);

        void onRemoveScanDone(String data);

        void onSearch(String data);

        void onClearText();
    }

    public interface OnTextChangedListener {
        void onTextChanged(String data);
    }

}
