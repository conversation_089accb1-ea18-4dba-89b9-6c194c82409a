package com.customer.widget;

import android.annotation.SuppressLint;
import android.content.Context;
import android.content.res.TypedArray;
import android.os.Handler;

import androidx.annotation.Nullable;
import androidx.core.content.ContextCompat;
import androidx.appcompat.widget.AppCompatEditText;
import androidx.appcompat.widget.AppCompatImageView;

import android.os.Looper;
import android.text.Editable;
import android.text.InputFilter;
import android.text.InputType;
import android.text.SpannableString;
import android.text.Spanned;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.text.style.AbsoluteSizeSpan;
import android.text.style.ForegroundColorSpan;
import android.util.AttributeSet;
import android.view.KeyEvent;
import android.view.View;

import com.customer.widget.core.LincBaseActivity;
import com.customer.widget.scanner.decoder.DriverLicense;
import com.customer.widget.scanner.style.IScanStyle;
import com.customer.widget.util.CommUtil;
import com.linc.platform.common.scanner.LabelField;
import com.linc.platform.utils.PermissionUtil;
import com.linc.platform.utils.StringUtil;
import com.data_collection.models.ActionType;
import com.data_collection.models.SendDataModel;
import com.data_collection.utils.ActionUtils;

import org.greenrobot.eventbus.EventBus;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;

import io.reactivex.rxjava3.core.Observable;
import io.reactivex.rxjava3.core.ObservableOnSubscribe;
import io.reactivex.rxjava3.schedulers.Schedulers;

/**
 * <AUTHOR>
 */
public class QuickScanner extends AbstractScanner {
    protected AppCompatEditText scanDataEdit;
    public AppCompatEditText inputDataEdit;
    private AppCompatImageView scanImg;

    private String hintText;
    private int hintTextSize;
    private int hintTextColor;
    private String scannerTitleText;
    private String scannerMessageText;
    private SpannableString hintSpannable;
    private List<Object> hintSpans = new ArrayList<>();
    private OnScanEvent scanEvent;
    private ScanBarcodeDialog scanBarcodeDialog;
    private boolean supportKeyBoardEnter = true;
    private boolean isRestrictInput = false;
    private boolean isAbleReplace = false;
    private boolean needReset = true;
    private boolean needHideKeyBoard = true;
    /**
     * scan的时候会触发onClick事件，因此需要此标记控制
     */
    private boolean canClick = true;

    private boolean isNewStyleOnV1 = false;
    private String lastInputText = "";
    private boolean isNewModel;
    private LabelField labelField;
    private boolean allowInputEmpty = false;

    private Runnable lastInputRunnable;

    private final Handler handler = new Handler(Looper.getMainLooper());

    public QuickScanner(Context context) {
        this(context, null);
    }

    public QuickScanner(Context context, @Nullable AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public QuickScanner(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        initView(context, attrs);
    }

    private void initView(Context context, AttributeSet attrs) {
        TypedArray a = getContext().obtainStyledAttributes(attrs, R.styleable.QuickScanner);
        hintText = a.getString(R.styleable.QuickScanner_hintText);
        hintTextSize = a.getDimensionPixelSize(R.styleable.QuickScanner_hintTextSize, -1);
        hintTextColor = a.getColor(R.styleable.QuickScanner_hintTextColor, -1);
        isNewStyleOnV1 = a.getBoolean(R.styleable.QuickScanner_newStyleOnV1, false);
        isNewModel = a.getBoolean(R.styleable.QuickScanner_isNewModel, false);
        scannerTitleText = a.getString(R.styleable.QuickScanner_scannerTitleText);
        scannerMessageText = a.getString(R.styleable.QuickScanner_scannerMessageText);
        labelField = LabelField.toLabelFiled(a.getInt(R.styleable.QuickScanner_scanLabel, -1));
        inflate(context, a.getResourceId(R.styleable.QuickScanner_layout_res, R.layout.view_quick_scanner), this);
        a.recycle();

        bindView();
        initHint();
        if (isNewModel) {
            initScanEditForNewModel();
            initInputEditForNewModel();
        } else {
            initScanEdit();
            initInputEdit();
        }
        initScanDialog();
        // 注释掉这行调用，避免重复上报问题
        // initCollectionDataInputListener 方法会导致与 onDataScanDone 方法重复上报数据
        // initCollectionDataInputListener();
        inputDataEdit.setOnFocusChangeListener((v, hasFocus) -> {
            if (scanEvent != null) {
                scanEvent.inputEditFocusChange(hasFocus);
            }
        });

        scanImg.setOnClickListener(v -> {
            if (PermissionUtil.requestCameraPermission(context)) {
                scanBarcodeDialog.show(((LincBaseActivity) context).getSupportFragmentManager(), "scan data");
                EventBus.getDefault().post(
                        new SendDataModel(ActionType.CLICK, ActionUtils.INSTANCE.getViewActionId(QuickScanner.this)));
            }
        });
    }

    private void initHint() {
        if (hintText != null && !hintText.isEmpty() && (hintTextSize != -1 || hintTextColor != -1)) {
            hintSpannable = new SpannableString(hintText);
            if (hintTextSize != -1) {
                AbsoluteSizeSpan sizeSpan = new AbsoluteSizeSpan(hintTextSize);
                hintSpans.add(sizeSpan);
            }
            if (hintTextColor != -1) {
                ForegroundColorSpan colorSpan = new ForegroundColorSpan(hintTextColor);
                hintSpans.add(colorSpan);
            }
            fillHintSpans(hintText.length());
            doSetHintText(hintSpannable);
        } else {
            doSetHintText(hintText);
        }
    }

    private void fillHintSpans(int length) {
        for (Object span : hintSpans) {
            hintSpannable.setSpan(span, 0, length, Spanned.SPAN_INCLUSIVE_EXCLUSIVE);
        }
    }

    private void initScanEdit() {
        scanDataEdit.setInputType(InputType.TYPE_NULL);
        scanDataEdit.setOnClickListener(v -> {
            if (canClick && !isRestrictInput) {
                scanDataEdit.setVisibility(GONE);
                inputDataEdit.setVisibility(VISIBLE);
                setFocus(inputDataEdit);
                CommUtil.showKeyBoard(inputDataEdit);
            }
            this.canClick = true;
        });

        scanDataEdit.setOnEditorActionListener((v, actionId, event) -> {
            processScan(scanDataEdit, isInputByScanner(event));
            return false;
        });
    }

    public void isAbleTextReplace(boolean isAble) {
        this.isAbleReplace = isAble;
    }

    @SuppressLint("ClickableViewAccessibility")
    private void initScanEditForNewModel() {
        scanDataEdit.setInputType(InputType.TYPE_NULL);
        scanDataEdit.setOnTouchListener((v, event) -> {
            if (event.getAction() == KeyEvent.ACTION_DOWN && !isRestrictInput) {
                scanDataEdit.setVisibility(GONE);
                inputDataEdit.setVisibility(VISIBLE);
                inputDataEdit.setText(scanDataEdit.getText() != null ? scanDataEdit.getText().toString() : "");
                setFocus(inputDataEdit);
                CommUtil.showKeyBoard(inputDataEdit);
            }
            return false;
        });

        scanDataEdit.setOnEditorActionListener((v, actionId, event) -> onEditorAction(scanDataEdit, event));
    }

    private boolean onEditorAction(AppCompatEditText editText, KeyEvent event) {
        if (event == null) {// called when soft keyboard action done
            processScan(editText, false);
            return false;
        } else {
            if (event.getAction() == KeyEvent.ACTION_DOWN) {// Avoid when scanning with small scanner
                // action_down、action_up is called once
                processScan(editText, true);
            }
        }
        return true;
    }

    private void processScan(AppCompatEditText scanDataEdit, boolean inputByScanner) {
        if (supportKeyBoardEnter)
            onDataScanDone(scanDataEdit.getText().toString().trim(), false, inputByScanner);
    }

    private void initInputEdit() {
        inputDataEdit.setOnEditorActionListener((v, actionId, event) -> {
            processInput(inputDataEdit, isInputByScanner(event));
            return false;
        });
    }

    private void initInputEditForNewModel() {
        inputDataEdit.setOnEditorActionListener((v, actionId, event) -> onEditorAction(inputDataEdit, event));
    }

    private void processInput(AppCompatEditText inputDataEdit, boolean inputByScanner) {
        if (supportKeyBoardEnter)
            onDataScanDone(inputDataEdit.getText().toString().trim(), false, inputByScanner);
    }

    private boolean isInputByScanner(KeyEvent event) {
        return event != null && KeyEvent.KEYCODE_ENTER == event.getKeyCode()
                && KeyEvent.ACTION_DOWN == event.getAction();
    }

    @Override
    public void setInputRestriction(boolean isRestrictInput) {
        this.isRestrictInput = isRestrictInput;
        scanDataEdit.setVisibility(this.isRestrictInput ? VISIBLE : GONE);
        inputDataEdit.setVisibility(this.isRestrictInput ? GONE : VISIBLE);
    }

    private void initScanDialog() {
        scanBarcodeDialog = new ScanBarcodeDialog();
        if (!TextUtils.isEmpty(scannerTitleText)) {
            scanBarcodeDialog.setScannerTitleText(scannerTitleText);
        }
        if (!TextUtils.isEmpty(scannerMessageText)) {
            scanBarcodeDialog.setScannerMessageText(scannerMessageText);
        }
        if (labelField != null) {
            scanBarcodeDialog.setLabelField(labelField);
        }
        scanBarcodeDialog.setOnBarcodeResult(new ScanBarcodeDialog.OnBarcodeResult() {
            @Override
            public void onBarcode(String barcode) {
                onDataScanDone(barcode, true, false);
            }

            @Override
            public void onDriverLicense(DriverLicense driverLicense) {

            }

            @Override
            public void onDismiss() {

            }
        });
    }

    private void bindView() {
        scanDataEdit = (AppCompatEditText) findViewById(R.id.scan_sn_edit);
        inputDataEdit = (AppCompatEditText) findViewById(R.id.input_sn_edit);
        scanImg = (AppCompatImageView) findViewById(R.id.scan_img);
    }

    public void setScanEvent(OnScanEvent scanEvent) {
        this.scanEvent = scanEvent;
    }

    public OnScanEvent getScanEvent() {
        return this.scanEvent;
    }

    public void setScanReset(boolean needReset) {
        this.needReset = needReset;
    }

    private void onDataScanDone(String data, boolean byScanBarcodeDialog, boolean inputByScanner) {
        this.canClick = false;

        inputDataEdit.setVisibility(GONE);
        scanDataEdit.setVisibility(VISIBLE);

        // When restricting input, clear the content of the input box through scanner
        // input
        if ((isRestrictInput || isAbleReplace) && inputByScanner && !StringUtil.equals(data, lastInputText)) {
            setText(data = StringUtil.replaceLast(data, lastInputText, ""));
        } else {
            lastInputText = data;
        }

        reset();
        if (scanEvent == null || (TextUtils.isEmpty(data) && !allowInputEmpty))
            return;
        EventBus.getDefault()
                .post(new SendDataModel(ActionType.INPUT, ActionUtils.INSTANCE.getViewActionId(this), data));
        if (byScanBarcodeDialog) {
            String finalData = data;
            new Handler().postDelayed(() -> scanEvent.onDone(this, finalData), 300);
        } else {
            scanEvent.onDone(this, data);
        }
    }

    public void reset() {
        if (needReset) {
            lastInputText = "";
            scanDataEdit.setText("");
            inputDataEdit.setText("");
        }
        setFocus(inputDataEdit.getVisibility() == VISIBLE ? inputDataEdit : scanDataEdit);
        if (needHideKeyBoard) {
            CommUtil.hideKeyBoard(inputDataEdit.getVisibility() == VISIBLE ? inputDataEdit : scanDataEdit);
        }
    }

    public void setNeedHideKeyBoard(boolean needHideKeyBoard) {
        this.needHideKeyBoard = needHideKeyBoard;
    }

    private void setFocus(View view) {
        view.setFocusable(true);
        view.setFocusableInTouchMode(true);
        view.requestFocus();
    }

    public void setInputFocus() {
        if (VISIBLE == inputDataEdit.getVisibility()) {
            setFocus(inputDataEdit);
        } else {
            setFocus(scanDataEdit);
        }
    }

    public void setHintText(CharSequence hintText) {
        if (hintSpannable != null) {
            hintSpannable = new SpannableString(hintText.toString());
            fillHintSpans(hintText.toString().length());
            doSetHintText(hintSpannable);
        } else {
            doSetHintText(hintText);
        }
    }

    public void setHintText(int resId) {
        CharSequence hintText = getContext().getResources().getText(resId);
        setHintText(hintText);
    }

    private void doSetHintText(CharSequence hintText) {
        scanDataEdit.setHint(hintText);
        inputDataEdit.setHint(hintText);
    }

    public String getText() {
        if (VISIBLE == inputDataEdit.getVisibility()) {
            return inputDataEdit.getText().toString().trim();
        } else {
            return scanDataEdit.getText().toString().trim();
        }
    }

    public void setText(String text) {
        lastInputText = text;
        inputDataEdit.setText(text);
        scanDataEdit.setText(text);
    }

    public void setInputType(int inputType) {
        scanDataEdit.setInputType(inputType);
        inputDataEdit.setInputType(inputType);
    }

    public void setTextSize(int size) {
        if (scanDataEdit != null) {
            scanDataEdit.setTextSize(size);
        }
        if (inputDataEdit != null) {
            inputDataEdit.setTextSize(size);
        }
    }

    public void setTextColor(int color) {
        if (scanDataEdit != null) {
            scanDataEdit.setTextColor(color);
        }
        if (inputDataEdit != null) {
            inputDataEdit.setTextColor(color);
        }
    }

    public void setInputSNEditMaxLength(int length) {
        inputDataEdit.setFilters(new InputFilter[]{new InputFilter.LengthFilter(length)});
    }

    public void setDisabled(boolean disabled) {
        setViewEnable(scanDataEdit, !disabled);
        setViewEnable(inputDataEdit, !disabled);
        setViewEnable(scanImg, !disabled);

        if (isNewStyleOnV1 && getContext() != null) {
            scanDataEdit.setHintTextColor(
                    ContextCompat.getColor(getContext(), disabled ? R.color.edit_enable_false_hint_v1 : R.color.white));
            inputDataEdit.setHintTextColor(
                    ContextCompat.getColor(getContext(), disabled ? R.color.edit_enable_false_hint_v1 : R.color.white));
        }
    }

    private void setViewEnable(View view, boolean enable) {
        view.setEnabled(enable);
        view.setFocusable(enable);
        view.setFocusableInTouchMode(enable);
    }

    public void setSupportKeyBoardEnter(boolean supportKeyBoardEnter) {
        this.supportKeyBoardEnter = supportKeyBoardEnter;
    }

    public void setScannerTitleText(String title) {
        scanBarcodeDialog.setScannerTitleText(title);
    }

    public void setScannerMessageText(String msgText) {
        scanBarcodeDialog.setScannerMessageText(msgText);
    }

    public void setScanDialogTitleView(IScanStyle sScanStyle) {
        if (null == scanBarcodeDialog) {
            return;
        }
        scanBarcodeDialog.setCustomScanTitleView(sScanStyle);
    }

    public void setLabelField(LabelField labelField) {
        this.labelField = labelField;
        scanBarcodeDialog.setLabelField(labelField);
    }

    public void setAllowInputEmpty(boolean allowInputEmpty) {
        this.allowInputEmpty = allowInputEmpty;
    }

    @SuppressLint("CheckResult")
    public void initCollectionDataInputListener() {
        Observable.create((ObservableOnSubscribe<String>) emitter -> {
                    inputDataEdit.addTextChangedListener(new TextWatcher() {
                        @Override
                        public void beforeTextChanged(CharSequence charSequence, int i, int i1, int i2) {
                        }

                        @Override
                        public void onTextChanged(CharSequence charSequence, int i, int i1, int i2) {
                        }

                        @Override
                        public void afterTextChanged(Editable editable) {
                            emitter.onNext(editable.toString());
                        }
                    });
                })
                .debounce(1, TimeUnit.SECONDS) // 防抖动1秒
                .filter(text -> !TextUtils.isEmpty(text))
                .subscribeOn(Schedulers.io()) // 在I/O线程中处理
                .subscribe(text -> {
                    // 在I/O线程中执行数据收集
                    EventBus.getDefault().post(
                            new SendDataModel(ActionType.INPUT, ActionUtils.INSTANCE.getViewActionId(QuickScanner.this),
                                    text));
                });
    }

    public interface OnScanEvent {
        void onDone(View view, String data);

        default void inputEditFocusChange(boolean focus) {
        }
    }
}
