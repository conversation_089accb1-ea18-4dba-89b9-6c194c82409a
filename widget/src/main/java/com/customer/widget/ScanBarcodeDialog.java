package com.customer.widget;

import android.content.DialogInterface;
import android.content.pm.ActivityInfo;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.app.AlertDialog;
import androidx.appcompat.widget.AppCompatImageView;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.constraintlayout.widget.ConstraintSet;
import androidx.core.content.ContextCompat;
import androidx.fragment.app.DialogFragment;
import androidx.fragment.app.FragmentManager;

import com.customer.widget.scanner.decoder.DriverLicense;
import com.customer.widget.scanner.style.DefaultScanStyle;
import com.customer.widget.scanner.style.IScanStyle;
import com.customer.widget.scanner.zxing.ZXingScannerView;
import com.google.zxing.BarcodeFormat;
import com.google.zxing.Result;
import com.linc.platform.common.scanner.LabelCode;
import com.linc.platform.common.scanner.LabelField;
import com.linc.platform.utils.Logger;
import com.linc.platform.utils.ToastUtil;
import com.qmuiteam.qmui.util.QMUIDisplayHelper;

/**
 * <AUTHOR>
 */

public class ScanBarcodeDialog extends DialogFragment implements ZXingScannerView.ResultHandler,
        DialogInterface.OnDismissListener {
    public static final int DRIVER_INFO_CODE = 1;

    private boolean showResult = false;
    private boolean isRescan = false;

    private OnBarcodeResult onBarcodeResult;
    private int barCodeFormat = 0;
    private ZXingScannerView zXingScannerView;
    private String resultTitleMessage;
    private int screenOrientation;
    private boolean isOnBackPressed = false;
    private String scannerViewText;
    private boolean squareViewFinder = false;
    private ConstraintLayout rootView;
    private IScanStyle<?> sScanStyle;
    private String scannerTitleText;
    private String scannerMessageText;
    private LabelField labelField;

    public ScanBarcodeDialog() {
        init(sScanStyle);
    }

    public void init(IScanStyle<?> sScanStyle) {
        if (sScanStyle == null) {
            sScanStyle = new DefaultScanStyle();
        }
        this.sScanStyle = sScanStyle;
    }


    public void setOnBarcodeResult(OnBarcodeResult onBarcodeResult) {
        this.onBarcodeResult = onBarcodeResult;
    }

    public void setRescan(boolean isRescan) {
        this.isRescan = isRescan;
    }

    public void setBarCodeFormat(int format) {
        barCodeFormat = format;
    }

    public void setShowResult(boolean showResult) {
        this.showResult = showResult;
    }

    public void setSquareViewFinder(boolean set) {
        this.squareViewFinder = set;
    }

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setStyle(DialogFragment.STYLE_NORMAL, android.R.style.Theme_Black_NoTitleBar_Fullscreen);
        resultTitleMessage = getContext().getResources().getString(R.string.title_is_check);
        setCancelable(true);
        screenOrientation = getActivity().getRequestedOrientation();
    }

    @Nullable
    @Override
    public View onCreateView(LayoutInflater inflater, @Nullable ViewGroup container,
                             @Nullable Bundle savedInstanceState) {
        rootView = (ConstraintLayout) inflater.inflate(R.layout.dialog_scan_barcode_v1, container, false);
        zXingScannerView = (ZXingScannerView) rootView.findViewById(R.id.scanner_preview);
        zXingScannerView.setScannerViewText(scannerViewText);
        zXingScannerView.setSquareViewFinder(squareViewFinder);
        AppCompatImageView flashIv = (AppCompatImageView) rootView.findViewById(R.id.switch_flash_btn);
        flashIv.setOnClickListener(v -> {
            zXingScannerView.switchFlash();
            boolean flashState = zXingScannerView.getFlashState();
            if (flashState) {
                flashIv.setImageResource(R.drawable.ic_flash_open);
            } else {
                flashIv.setImageResource(R.drawable.ic_flash_close);
            }
        });
        rootView.findViewById(R.id.cancel_scan_btn).setOnClickListener(v -> {
            dismiss();
        });
        return rootView;
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        setScannerTitleView();
    }

    @Override
    public void onResume() {
        zXingScannerView.setResultHandler(this);
        zXingScannerView.startCamera();
        super.onResume();

        getDialog().setOnKeyListener((dialog, keyCode, event) -> {
            if (keyCode == android.view.KeyEvent.KEYCODE_BACK) {
                dismissAllowingStateLoss();
                isOnBackPressed = true;
                return true;
            } else {
                return false;
            }
        });

        if (barCodeFormat == DRIVER_INFO_CODE) {
            getActivity().setRequestedOrientation(ActivityInfo.SCREEN_ORIENTATION_LANDSCAPE);
        }
    }

    @Override
    public void onPause() {
        zXingScannerView.stopCamera();
        zXingScannerView.setResultHandler(null);
        super.onPause();
    }

    @Override
    public void onDestroyView() {
        getActivity().setRequestedOrientation(screenOrientation);
        super.onDestroyView();
    }

    @Override
    public void handleResult(Result rawResult) {
        if (onBarcodeResult == null) {
            return;
        }
        final DriverLicense driverLicense;
        final LabelCode labelCode;

        if (barCodeFormat == DRIVER_INFO_CODE
                && rawResult.getBarcodeFormat().equals(BarcodeFormat.PDF_417)) {
            driverLicense = new DriverLicense(rawResult.getText());
        } else {
            driverLicense = null;
        }
        if(rawResult.getBarcodeFormat().equals(BarcodeFormat.QR_CODE)){
            labelCode = new LabelCode(rawResult.getText(),this.labelField);
        }else{
            labelCode = null;
        }
        if (showResult) {
            String result = "";
            if (driverLicense != null) {
                result = driverLicense.toJson();
            } else {
                result = rawResult.getText();
            }

            AlertDialog alertDialog = GeneralAlertDialog.createAlertDialog(getContext(),
                    resultTitleMessage,
                    result,
                    (dialog, which) -> {
                    }, (dialog, which) -> {
                        if (barCodeFormat == DRIVER_INFO_CODE
                                && rawResult.getBarcodeFormat().equals(BarcodeFormat.PDF_417)
                                && driverLicense != null) {
                            onBarcodeResult.onBarcode(driverLicense.getDriverLicenseNumber());
                            onBarcodeResult.onDriverLicense(driverLicense);
                        } else if (rawResult.getBarcodeFormat().equals(BarcodeFormat.QR_CODE) && labelCode != null) {
                            onBarcodeResult.onBarcode(labelCode.getText());
                        } else {
                            onBarcodeResult.onBarcode(rawResult.getText());
                        }
                    }
            );
            alertDialog.setOnDismissListener(this);
            alertDialog.show();
        } else {
            if (barCodeFormat == DRIVER_INFO_CODE
                    && rawResult.getBarcodeFormat().equals(BarcodeFormat.PDF_417)
                    && driverLicense != null) {
                onBarcodeResult.onBarcode(driverLicense.getDriverLicenseNumber());
                onBarcodeResult.onDriverLicense(driverLicense);
            } else if ((rawResult.getBarcodeFormat().equals(BarcodeFormat.QR_CODE) && labelCode != null)) {
                onBarcodeResult.onBarcode(labelCode.getText());
            } else {
                onBarcodeResult.onBarcode(rawResult.getText());
            }

            if (!isRescan) {
                dismissAllowingStateLoss();
            }
        }
        zXingScannerView.stopCameraPreview();
    }

    @Override
    public void show(FragmentManager manager, String tag) {
        try {
            if (!isAdded()) {
                manager.beginTransaction().remove(this).commit();
                super.show(manager, tag);
            }
        } catch (Exception e) {
            Logger.e("camera open error:", e.getMessage());
            ToastUtil.showToast(R.string.hint_camera_is_not_ready);
        }
    }

    @Override
    public void onDismiss(DialogInterface dialog) {
        if (isRescan && !isOnBackPressed) {
            zXingScannerView.resumeCameraPreview(this);
        } else {
            if (onBarcodeResult != null) {
                onBarcodeResult.onDismiss();
            }

            if (getFragmentManager() != null) {
                dismissAllowingStateLoss();
            }
        }

        if (isOnBackPressed) {
            isOnBackPressed = false;
        }
    }

    public void setScannerViewText(String scannerViewText) {
        this.scannerViewText = scannerViewText;
    }

    private void setScannerTitleView() {
        if (TextUtils.isEmpty(scannerTitleText)) {
            return;
        }
        createScannerTitleView();
        View titleView = rootView.findViewById(R.id.scan_title_id);
        if (titleView != null && titleView instanceof TextView) {
            ((TextView) titleView).setText(scannerTitleText);
        }
        TextView messageTextTv = rootView.findViewById(R.id.scan_tip);
        if (!TextUtils.isEmpty(scannerMessageText)) {
            messageTextTv.setText(scannerMessageText);
        }
    }

    private void createScannerTitleView() {
        View view = sScanStyle.createView(getContext());
        view.setId(R.id.scan_title_id);
        view.setBackgroundColor(ContextCompat.getColor(getContext(), R.color.color_black_70));
        rootView.addView(view);
        ConstraintSet set = new ConstraintSet();
        set.clone(rootView);
        set.connect(
                R.id.scan_title_id, ConstraintSet.TOP, ConstraintSet.PARENT_ID, ConstraintSet.TOP,
                QMUIDisplayHelper.dp2px(getContext(), 86)
        );
        set.connect(
                R.id.scan_title_id, ConstraintSet.START, ConstraintSet.PARENT_ID, ConstraintSet.START,
                QMUIDisplayHelper.dp2px(getContext(), 16)
        );
        set.connect(
                R.id.scan_title_id, ConstraintSet.END, ConstraintSet.PARENT_ID, ConstraintSet.END,
                QMUIDisplayHelper.dp2px(getContext(), 16)
        );
        set.applyTo(rootView);
    }

    public void setScannerTitleText(String text) {
        this.scannerTitleText = text;
    }

    public void setScannerMessageText(String message) {
        this.scannerMessageText = message;
    }

    public void setCustomScanTitleView(IScanStyle<?> sScanStyle) {
        if (null == sScanStyle) {
            return;
        }
        this.sScanStyle = sScanStyle;
    }

    public void setLabelField(LabelField labelField){
        this.labelField = labelField;
    }

    public interface OnBarcodeResult {
        void onBarcode(String barcode);

        void onDriverLicense(DriverLicense driverLicense);

        void onDismiss();
    }
}
