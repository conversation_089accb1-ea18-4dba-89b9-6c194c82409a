package com.unis.cyclecount.core.work.countitem

import com.unis.cyclecount.core.work.Page
import com.unis.platform.cyclecount.model.CountInventoryEntity
import com.unis.platform.cyclecount.model.CountResultCreateEntity
import com.unis.platform.cyclecount.model.ItemUomEntity
import com.unis.reactivemvi.mvi.ReactiveDataState
import com.unis.reactivemvi.mvi.ReactiveUiState
import com.unis.reactivemvi.mvi.UiEvent
import java.util.*

/**
 * @Description:
 * @Author: Dennis
 * @CreateDate: 2024/7/29
 */
data class CountItemDataState(
    val selectedItemUomEntity: ItemUomEntity? = null,
    val countItemCompose: CountItemCompose = CountItemCompose(),
) : ReactiveDataState {


}

data class CountItemUiState(
    val selectedItemUomEntity: ItemUomEntity? = null,
    val countItemCompose: CountItemCompose? = null,
) : ReactiveUiState {

}

data class CountItemCompose(
        val expirationDate: Date? = null,
        val mfgDate: Date? = null,
        val shelfLifeDays: String? = null,
        val qty: String? = null,
        val lotNo: String? = null,
        val title: String? = null,
)

interface CountItemEvent {

    data class RecountAndOverrideLotNoDialog(val msg: String): UiEvent

    data class AddLotNoInventoryDialog(val msg: String): UiEvent

    data class ReRenderView(val page: Page): UiEvent

    data class ReCycleCount(val msg: String): UiEvent

    data class ShowNoMatchSimpleQtyCountDialog(val isPickLocation: Boolean): UiEvent

    data class ShowCloseTaskDialog(val itemId: String, val msg: String): UiEvent

    data class ShowItemLpsDialog(val countInventories: List<CountInventoryEntity>): UiEvent

    object ClosedTask : UiEvent
}