package com.unis.cyclecount.core.work.countitem

import android.text.TextUtils
import androidx.fragment.app.FragmentManager
import com.customer.widget.common.addToNewList
import com.linc.platform.utils.ToastUtil.showToast
import com.unis.cyclecount.R
import com.unis.cyclecount.core.BackProcessData
import com.unis.cyclecount.core.CycleCountViewModel
import com.unis.cyclecount.core.countresult.CountResultHandlerViewModel
import com.unis.cyclecount.core.newitem.NewItemWorkViewModel
import com.unis.cyclecount.core.work.CountValidator
import com.unis.cyclecount.core.work.CountValidator.Companion.isNeedRecountAndOverrideLotNo
import com.unis.cyclecount.core.work.CycleCountWorkViewModel
import com.unis.cyclecount.core.work.Page
import com.unis.cyclecount.core.work.ProcessChange
import com.unis.platform.cyclecount.api.CycleCountApi
import com.unis.platform.cyclecount.api.CycleCountBamApi
import com.unis.platform.cyclecount.model.CountInventoryEntity
import com.unis.platform.cyclecount.model.CountInventoryQueryEntity
import com.unis.platform.cyclecount.model.CountMethod
import com.unis.platform.cyclecount.model.CountProcessDataEntity
import com.unis.platform.cyclecount.model.CountResultCreateEntity
import com.unis.platform.cyclecount.model.CountResultEntity
import com.unis.platform.cyclecount.model.CountResultQueryEntity
import com.unis.platform.cyclecount.model.CountResultTypeEntity
import com.unis.platform.cyclecount.model.ItemUomEntity
import com.unis.platform.inventory.model.InventoryStatusEntity
import com.unis.platform.location_v2.model.LocationEntity
import com.unis.platform.location_v2.model.LocationType
import com.unis.platform.util.UomUtils
import com.unis.reactivemvi.SnackType
import com.unis.reactivemvi.common.DomainType
import com.unis.reactivemvi.mvi.ReactiveViewModel
import com.unis.reactivemvi.mvi.mapDataToUi
import com.unis.reactivemvi.mvvm.kotlin.BaseRepository
import com.unis.reactivemvi.mvvm.kotlin.BaseVM
import com.unis.reactivemvi.mvvm.kotlin.extensions.apiServiceLazy
import com.unis.reactivemvi.mvvm.kotlin.extensions.getString
import com.unis.reactivemvi.mvvm.kotlin.extensions.launch
import com.unis.reactivemvi.mvvm.kotlin.extensions.showSnack
import com.unis.reactivemvi.mvvm.kotlin.extensions.showToast
import java.util.Date

/**
 * @Description:
 * @Author: Dennis
 * @CreateDate: 2024/7/29
 */
class CountItemViewModel(
    private val isNewItem: Boolean,
    private val rootParentViewModel: BaseVM,
    initialDataState: CountItemDataState = CountItemDataState(),
    initialUiState: CountItemUiState = CountItemUiState()
    ) : ReactiveViewModel<CountItemDataState, CountItemUiState>(initialDataState, initialUiState) {

    private val activityViewModel = getActivityViewModel()
    private val countProcessDataEntity = activityViewModel.getCountProcessDataEntity()
    private val repository: Repository = Repository()
    private val countResultHandlerViewModel: CountResultHandlerViewModel = CountResultHandlerViewModel()
    val countMethod = countProcessDataEntity.countTaskEntity.countTicket?.countMethod

    init {
        autoUpdateDataToUi()
        setInitUnit()
    }

    private fun autoUpdateDataToUi() {
        mapDataToUi(CountItemDataState::selectedItemUomEntity, CountItemUiState::selectedItemUomEntity){
            it
        }
        mapDataToUi(CountItemDataState::countItemCompose, CountItemUiState::countItemCompose){
            it
        }
    }

    fun getActivityViewModel(): CycleCountViewModel {
        return if (isNewItem) {
            (rootParentViewModel as NewItemWorkViewModel).getActivityViewModel()
        } else {
            (rootParentViewModel as CycleCountWorkViewModel).getActivityViewModel()
        }
    }

    private fun setInitUnit() {
        val unitOptions = getUnitOptions()
        if (unitOptions.isNotEmpty()) {
            val selectedUnitIndex = getSelectedUnitIndex(unitOptions)
            setSelectedUnit(unitOptions[selectedUnitIndex])
        }
    }

    fun setSelectedUnit(itemUomEntity: ItemUomEntity) {
        setDataState { copy(selectedItemUomEntity = itemUomEntity) }
    }

    fun getUnitOptions(): List<ItemUomEntity> {
        val itemInventories = getCountProcessDataEntity().countInventories?.filter { ivn -> ivn.itemId == getCurrentItemSpec()?.id }
        val uomIds = itemInventories?.map { v -> v.uomId }?.distinct()
        if (uomIds.isNullOrEmpty()) return countProcessDataEntity.itemUomList?: listOf()
        return countProcessDataEntity.itemUomList?.filter { uom-> uomIds?.contains(uom.uomId) == true }?: listOf()
    }

    fun getBaseUnit(): ItemUomEntity? {
        return getUnitOptions().find { v -> v.isBaseUom }
    }

    fun isBaseAndOnlyOneUnit(): Boolean {
        val unitEntries = getUnitOptions()
        return unitEntries.size == 1 && unitEntries.all { v -> v.isBaseUom }
    }

    private fun getSelectedUnitIndex(unitEntries: List<ItemUomEntity>?): Int {
        if (unitEntries.isNullOrEmpty()) {
            return 0
        }
        val countedUnitId = getCountedUnitId(getCountProcessDataEntity().item?.id, getCountProcessDataEntity().countResults)
        for (i in unitEntries.indices) {
            if (countedUnitId == unitEntries[i].uomId) {
                return i
            }
        }
        return 0
    }

    private fun getCountedUnitId(itemId: String?, countResultEntities: List<CountResultEntity>?): String {
        itemId ?: return ""
        countResultEntities ?: return ""
        val sortedCountResultEntities = countResultEntities.sortedBy { v -> v.sortedByDate }.reversed()
        return sortedCountResultEntities.find { v -> v.itemId == itemId }?.countUomId ?: ""
    }

    fun getCountProcessDataEntity() = countProcessDataEntity


    fun isNewItem() = isNewItem

    fun getCurrentLocation(): LocationEntity? {
        return if (isNewItem) {
            (rootParentViewModel as NewItemWorkViewModel).getCurrentLocation()
        } else {
            (rootParentViewModel as CycleCountWorkViewModel).getCurrentLocation()
        }
    }

    fun getCurrentLp(): String {
        return getCountProcessDataEntity().lpId?: ""
    }

    fun getCurrentItemSpec() = getCountProcessDataEntity().item

    fun getSelectedUnit() = dataState.selectedItemUomEntity

    fun isRequireCollectLotNo() = getCountProcessDataEntity().isRequireCollectLotNo()

    fun isRequireCollectMfgDate() = getCountProcessDataEntity().isRequireCollectMfgDate()

    fun isRequireCollectExpirationDate() = getCountProcessDataEntity().isRequireCollectExpirationDate()

    fun isRequireCollectShelfLifeDaysDate() = getCountProcessDataEntity().isRequireCollectShelfLifeDays()

    fun isBlind() = getCountProcessDataEntity().isBlind()

    fun getRequireCountQtys(): List<String>? {
        return getCountProcessDataEntity().countInventories
            ?.filter { v -> isMatch(v) }
            ?.map { v -> "${v.qty}${v.uom}" }
    }

    private fun isMatch(countInventoryEntity: CountInventoryEntity): Boolean {
        return if (getCountProcessDataEntity().isPickLocation()) {
            getCurrentLocation()?.id == countInventoryEntity.locationId
                    && getCurrentItemSpec()?.id == countInventoryEntity.itemId
        } else {
            getCurrentLocation()?.id == countInventoryEntity.locationId
                    && getCurrentLp() == countInventoryEntity.lpId
                    && getCurrentItemSpec()?.id == countInventoryEntity.itemId
        }
    }

    fun getExpirationDate() = dataState.countItemCompose.expirationDate

    fun saveExpirationDate(expirationDate: Date?) {
        val countItemCompose = dataState.countItemCompose.copy(expirationDate = expirationDate)
        setDataState { copy(countItemCompose = countItemCompose) }
    }

    fun getMfgDate() = dataState.countItemCompose.mfgDate

    fun saveMfgDate(mfgDate: Date?) {
        val countItemCompose = dataState.countItemCompose.copy(mfgDate = mfgDate)
        setDataState { copy(countItemCompose = countItemCompose) }
    }

    private fun getQty() = dataState.countItemCompose.qty

    fun saveQty(qty: String?) {
        val countItemCompose = dataState.countItemCompose.copy(qty = qty)
        setDataState { copy(countItemCompose = countItemCompose) }
    }

    private fun getShelfLifeDays() = dataState.countItemCompose.shelfLifeDays

    fun saveShelfLifeDays(shelfLifeDays: String?) {
        val countItemCompose = dataState.countItemCompose.copy(shelfLifeDays = shelfLifeDays)
        setDataState { copy(countItemCompose = countItemCompose) }
    }

    private fun getLotNo() = dataState.countItemCompose.lotNo

    fun saveLotNo(lotNo: String?) {
        val countItemCompose = dataState.countItemCompose.copy(lotNo = lotNo)
        setDataState { copy(countItemCompose = countItemCompose) }
    }

    fun setIsRecount(recount: Boolean) {
        countProcessDataEntity.setIsRecount(recount)
    }

    fun submitCounted(isNeedCheckQtyMatch: Boolean = true) {
        val errMsg = validate()
        if (errMsg.isNullOrEmpty()) {
            val itemId = countProcessDataEntity.item?.id
            val qty = getQty()?.toDouble() ?: 0.0
            val selectedUnit = getSelectedUnit()
            if (isNeedCheckQtyMatch && countMethod == CountMethod.SIMPLE_QTY_COUNT
                && !isCountedQtyMatch(itemId, qty, selectedUnit?.uomId)
                && !countProcessDataEntity.isCountedNoMatchInCurrentLocation(itemId)
                && !countProcessDataEntity.isRecount()) {
                fireEvent { CountItemEvent.ShowNoMatchSimpleQtyCountDialog(countProcessDataEntity.isPickLocation()) }
                return
            }
            val itemCountPropertyCompose = CountProcessDataEntity.ItemCountPropertyCompose(
                qty = qty,
                lotNo = getLotNo(),
                expirationDate = getExpirationDate(),
                mfgDate = getMfgDate(),
                shelfLifeDays = (getShelfLifeDays() ?: "0").toInt()
            )
            countProcessDataEntity.updateSelectedItemUom(selectedUnit)
            countProcessDataEntity.updateItemCountPropertyCompose(itemCountPropertyCompose)
            val isShowItemLpsDialog = !countProcessDataEntity.isPickLocation()
                    && countMethod == CountMethod.SIMPLE_QTY_COUNT
                    && isCountedQtyMatch(itemId, qty, selectedUnit?.uomId)
            validateSubmitProcess(isShowItemLpsDialog)
        } else {
            showToast(errMsg)
        }

    }

    private fun validateSubmitProcess(isShowItemLpsDialog: Boolean) {
        val errMsg = if (!isNewItem()) CountValidator.validateSubmitProcess(getCountProcessDataEntity()) else null
        when {
            errMsg.isNullOrEmpty() -> { 
                if (isShowItemLpsDialog) {
                    val currentItemId = getCountProcessDataEntity().item?.id
                    val relevantInventories = getCountProcessDataEntity().countInventories?.filter { it.itemId == currentItemId }

                    val mergedInventories = relevantInventories?.toList()
                        ?.groupBy { Pair(it.lpId, it.uomId) }
                        ?.map { (_, group) ->
                            group.first().copy(
                                qty = group.sumOf { it.qty },
                                baseQty = group.sumOf { it.baseQty }
                            )
                        }
                    fireEvent { CountItemEvent.ShowItemLpsDialog(mergedInventories?: listOf()) }
                } else {
                    onStartSubmit(false)
                }
             }
            isNeedRecountAndOverrideLotNo(errMsg) -> showRecountAndOverrideLotNoDialog(errMsg)
            else -> { onErrorHandle(errMsg) }
        }
    }

    fun submitOverride() {
        getResultIdsForDeleteByLotNo()?.let {
            deleteCountEntryRecord(it){
                onSubmit()
            }
        }
    }

    private fun getResultIdsForDeleteByLotNo(): List<String>? {
        val countProcessDataEntity = getCountProcessDataEntity()
        val lotNo = countProcessDataEntity.itemCountPropertyCompose?.lotNo
        return countProcessDataEntity.countResults?.filter { v -> v.lotNo == lotNo }?.map { v -> v.id }
    }

    private fun getResultIdsForDeleteByLp(): List<String>? {
        val countProcessDataEntity = getCountProcessDataEntity()
        val lpId = countProcessDataEntity.lpId
        return countProcessDataEntity.countResults?.filter { v -> v.lpId == lpId }?.map { v -> v.id }
    }

    fun onStartSubmit(isBatchCreate: Boolean = false) {
        if (getCountProcessDataEntity().isOverwrite()) {
            getResultIdsForDeleteByLp()?.let {
                deleteCountEntryRecord(it){
                    onSubmit(isBatchCreate)
                }
            }
        } else {
            onSubmit(isBatchCreate)
        }
    }

    private fun deleteCountEntryRecord(resultIds: List<String>, success: () -> Unit) {
        launch {
            requestAwait(repository.deleteCountResultRecord(resultIds)).onSuccess {
                getCountProcessDataEntity().updateIsOverwrite(false)
                success.invoke()
            }
        }
    }

    private fun onSubmit(isBatchCreate: Boolean = false) {
        val ticketId = countProcessDataEntity.countTaskEntity.ticketId
        val taskId = countProcessDataEntity.countTaskEntity.id
        val itemId = countProcessDataEntity.item?.id ?: ""

        launch {
            if (isBatchCreate) {
                val submitEntries = buildBatchSubmitEntries(getCountProcessDataEntity())
                requestAwait(repository.batchCreateCount(ticketId, taskId, submitEntries)).onFailure {
                    return@launch
                }
            } else {
                val submitEntry = buildSubmitEntry(getCountProcessDataEntity())
                requestAwait(repository.createCount(ticketId, taskId, submitEntry)).onFailure {
                    return@launch
                }
            }
            
            if (isNewItem) countProcessDataEntity.newItemCode = null
            countResultHandlerViewModel.searchCountResultWithUpdateProcessData(getCountProcessDataEntity()) {
                if (isNewItem) {
                    handleAfterSubmit(itemId)
                    return@searchCountResultWithUpdateProcessData
                }
                checkAllCountedForTask { isAllCounted ->
                    if (isAllCounted) {
                        fireEvent { CountItemEvent.ShowCloseTaskDialog(itemId, getString(R.string.msg_all_counted_close_task)) }
                    } else {
                        handleAfterSubmit(itemId)
                    }
                }
            }
        }
    }

    fun handleAfterSubmit(itemId: String) {
        if (countMethod == CountMethod.SIMPLE_QTY_COUNT) {
            if (getCurrentLocation()?.type == LocationType.PICK || !countProcessDataEntity.isCountedNoMatchInCurrentLocation(itemId)) {
                showScanItemFragment()
            } else {
                simpleQtyCountShowScanLpFragment()
            }
            countProcessDataEntity.clearLpAndItem()
            showSnack(SnackType.SuccessV1(), if (isNewItem()) R.string.msg_the_new_item_has_been_counted else R.string.msg_item_has_been_counted)
        } else {
            if (getCurrentLocation()?.type == LocationType.PICK) {
                countProcessDataEntity.clearItem()
                showScanItemFragment()
            } else {
                countProcessDataEntity.clearLpAndItem()
                showScanLpFragment()
            }
            showSnack(SnackType.SuccessV1(), if (isNewItem()) R.string.msg_the_new_item_has_been_counted else R.string.msg_item_has_been_counted)
        }
    }

    private fun checkAllCountedForTask(action: (Boolean) -> Unit) {
        CountValidator.checkAllCountedForTask(countProcessDataEntity) {
            when(it) {
                CountValidator.CAN_IMMEDIATE_CLOSE_TASK -> action.invoke(true)
                CountValidator.NEED_CHECK_ALL_COUNTED -> checkAllCountedProcess(action)
            }
        }
    }

    private fun checkAllCountedProcess(action: (Boolean) -> Unit) {
        searchTaskAllLocationInventory {
            val isAllCounted =
                CountValidator.isAllCountedForTask(it, countProcessDataEntity.countResults, countProcessDataEntity, countMethod)
            if (isAllCounted) {
                action.invoke(true)
            } else {
                action.invoke(false)
            }
        }
    }

    private fun searchTaskAllLocationInventory(action: (List<CountInventoryEntity>?) -> Unit) {
        launch {
            requestAwait(repository.searchInventory(CountInventoryQueryEntity().apply {
                if (!countProcessDataEntity.getTaskAllCountLocationId().isNullOrEmpty()) {
                    this.locationIds = countProcessDataEntity.getTaskAllCountLocationId()
                }
                if (countProcessDataEntity.isCountSpecialItem()) {
                    this.itemIds = countProcessDataEntity.getRequireCountItemIds()
                }
                this.customerId = countProcessDataEntity.customerId
                this.excludeStatuses = listOf(
                    InventoryStatusEntity.PICKED,
                    InventoryStatusEntity.PACKED,
                    InventoryStatusEntity.LOADED,
                    InventoryStatusEntity.SHIPPED,
                    InventoryStatusEntity.ADJUSTOUT
                )
            })).onSuccess {
                val sortedInventoryByLocationName = it?.filter { v -> !TextUtils.isEmpty(v.locationId) }?.sortedBy { v -> v.locationName }
                action.invoke(sortedInventoryByLocationName)
            }
        }
    }

    fun closeTask() {
        launch {
            requestAwait(repository.closeTask(countProcessDataEntity.countTaskEntity.id)).onSuccess {
                showToast(com.unis.cyclecount.R.string.close_success)
                fireEvent { CountItemEvent.ClosedTask }
            }
        }
    }

    private fun isCountedQtyMatch(itemId: String?, countedQty: Double?, countUomId: String?): Boolean {
        if (itemId.isNullOrEmpty() || countedQty == null || countUomId.isNullOrEmpty()) return false
        val currentCountInventories = getCountProcessDataEntity().countInventories?.filter { it.itemId == itemId }
        val neeCountBaseQty = currentCountInventories?.sumOf { it.baseQty }
        val countedBaseQty = UomUtils.calculateBaseQty(countedQty, countUomId)
        return neeCountBaseQty == countedBaseQty
    }

    private fun isMatchCurrentCount(): Boolean {
        val locationId = getCountProcessDataEntity().location?.id
        val lpId = getCountProcessDataEntity().lpId
        val itemId = getCountProcessDataEntity().item?.id
        val lotNo = getCountProcessDataEntity().itemCountPropertyCompose?.lotNo
        val uomId = getCountProcessDataEntity().selectedItemUom?.uomId
        val countedResults = getCountProcessDataEntity().countResults
        val currentCount = countedResults?.find { it.locationId == locationId && it.lpId == lpId && it.itemId == itemId
                && it.lotNo == lotNo && it.countUomId == uomId }
        return currentCount?.type == CountResultTypeEntity.MATCH
    }

    private fun simpleQtyCountShowScanLpFragment() {
        if (isNewItem()) {
            (rootParentViewModel as NewItemWorkViewModel).onCloseNewItemFragment()
            activityViewModel.popStepExcludeLocationFragment()
        } else {
            fireEvent { CountItemEvent.ReRenderView(Page.ScanItem) }
            (rootParentViewModel as CycleCountWorkViewModel).popStepExcludeLocationFragmentAndGoToScanLpFragment()
        }
    }

    private fun onErrorHandle(errMsg: String) {
        showSnack(SnackType.ErrorV2(), errMsg)
    }

    private fun showScanItemFragment() {
        if (isNewItem()) {
            (rootParentViewModel as NewItemWorkViewModel).onCloseNewItemFragment()
            activityViewModel.notifyPageReRenderView(Page.ScanItem)
        } else {
            fireEvent { CountItemEvent.ReRenderView(Page.ScanItem) }
            (rootParentViewModel as CycleCountWorkViewModel).onBackProcessTo(ProcessChange.ScanItem)
        }
    }

    private fun showScanLpFragment() {
        if (isNewItem()) {
            (rootParentViewModel as NewItemWorkViewModel).onCloseNewItemFragment()
            activityViewModel.notifyPageReRenderView(Page.ScanLp)
            activityViewModel.onBackProcessToEvent(BackProcessData(ProcessChange.ScanLp))
        } else {
            fireEvent { CountItemEvent.ReRenderView(Page.ScanLp) }
            (rootParentViewModel as CycleCountWorkViewModel).onBackProcessTo(ProcessChange.ScanItem, FragmentManager.POP_BACK_STACK_INCLUSIVE)
        }
    }

    private fun buildSubmitEntry(countProcessDataEntity: CountProcessDataEntity): CountResultCreateEntity {
        val itemCountPropertyCompose = countProcessDataEntity.itemCountPropertyCompose
        return CountResultCreateEntity().apply {
            this.locationId = countProcessDataEntity.location?.id
            this.itemId = countProcessDataEntity.item?.id
            this.countItemQty = itemCountPropertyCompose?.qty
            this.countUomId = countProcessDataEntity.selectedItemUom?.uomId
            this.uom = countProcessDataEntity.selectedItemUom?.name
            this.isEmptyLocation = false
            if (countProcessDataEntity.isPickLocation()) {
                this.lpId = countProcessDataEntity.location?.hlpId
            } else {
                this.lpId = countProcessDataEntity.lpId
            }
            if (isNewItem() && isRequireCollectLotNo()) {
                this.lotNo = itemCountPropertyCompose?.lotNo
            }
            if (isNewItem() && isRequireCollectExpirationDate()) {
                this.expirationDate = itemCountPropertyCompose?.expirationDate
            }
            if (isNewItem() && isRequireCollectMfgDate()) {
                this.mfgDate = itemCountPropertyCompose?.mfgDate
            }
            if (isNewItem() && isRequireCollectShelfLifeDaysDate()) {
                this.shelfLifeDays = itemCountPropertyCompose?.shelfLifeDays
            }
        }
    }

    private fun buildBatchSubmitEntries(countProcessDataEntity: CountProcessDataEntity): List<CountResultCreateEntity> {
        val filteredCountInventories = countProcessDataEntity.countInventories?.filter { v -> v.itemId == countProcessDataEntity.item?.id }
        val mergedInventories = filteredCountInventories?.toList()
            ?.groupBy { Pair(it.lpId, it.uomId) }
            ?.map { (_, group) ->
                group.first().copy(
                    qty = group.sumOf { it.qty },
                    baseQty = group.sumOf { it.baseQty }
                )
            }
        return mergedInventories?.map { v -> CountResultCreateEntity().apply {
            this.locationId = countProcessDataEntity.location?.id
            this.itemId = v.itemId
            this.countItemQty = v.qty
            this.countUomId = v.uomId
            this.uom = v.uom
            this.lpId = v.lpId
            this.isEmptyLocation = false
        } }?: listOf()
    }

    private fun showRecountAndOverrideLotNoDialog(errMsg: String) {
        fireEvent { CountItemEvent.RecountAndOverrideLotNoDialog(errMsg) }
    }

    private fun validate(): String? {
        val qty = getQty()
        if (qty.isNullOrEmpty() || qty.toInt() == 0) {
            return getString(R.string.msg_please_input_quantity)
        }
        val lotNo = getLotNo()
        if (isNewItem() && isRequireCollectLotNo() && lotNo.isNullOrEmpty()) {
            return getString(R.string.hint_lot_no)
        }
        val expirationDate = getExpirationDate()
        if (isNewItem() && isRequireCollectExpirationDate() && expirationDate == null) {
            return getString(R.string.msg_please_select_expiration_date)
        }
        val mfgDate = getMfgDate()
        if (isNewItem() && isRequireCollectMfgDate() && mfgDate == null) {
            return getString(R.string.msg_please_select_mfg_date)
        }
        val shelfLifeDays = getShelfLifeDays()
        if (isNewItem() && isRequireCollectShelfLifeDaysDate() && (shelfLifeDays.isNullOrEmpty() || shelfLifeDays.toInt() == 0)) {
            return getString(R.string.msg_please_input_shelf_life_days)
        }
        return null
    }

}

private class Repository : BaseRepository() {
    private val cycleCountApi by apiServiceLazy<CycleCountApi>(DomainType.CYCLE_COUNT)
    private val cycleCountBamApi by apiServiceLazy<CycleCountBamApi>(DomainType.CYCLE_COUNT)

    fun createCount(ticketId: String, taskId: String, createEntity: CountResultCreateEntity) = rxRequest2(cycleCountApi.createCount(ticketId, taskId, createEntity))

    fun batchCreateCount(ticketId: String, taskId: String, createEntities: List<CountResultCreateEntity>) = rxRequest2(cycleCountApi.batchCreateCount(ticketId, taskId, createEntities))

    fun deleteCountResultRecord(resultIds: List<String>) = rxRequest2(cycleCountApi.deleteCountResultRecord(
        CountResultQueryEntity().apply {
        this.ids = resultIds
    }))

    fun searchInventory(countInventoryQueryEntity: CountInventoryQueryEntity) = rxRequest2(cycleCountBamApi.searchInventory(countInventoryQueryEntity))

    fun closeTask(taskId: String) = rxRequest2(cycleCountApi.closeTask(taskId))

}