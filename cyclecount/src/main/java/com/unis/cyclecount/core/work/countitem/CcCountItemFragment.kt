package com.unis.cyclecount.core.work.countitem

import android.os.Bundle
import android.text.Editable
import android.text.TextUtils
import android.text.TextWatcher
import android.text.format.DateFormat
import com.customer.widget.util.CommUtil
import com.unis.cyclecount.core.newitem.NewItemWorkViewModel
import com.unis.cyclecount.core.work.CycleCountWorkViewModel
import com.unis.cyclecount.databinding.FragmentCcCountItemBinding
import com.customer.widget.common.CenterDialog
import com.customer.widget.extensions.setGone
import com.customer.widget.extensions.setVisible
import com.customer.widget.extensions.setVisibleOrGone
import com.unis.cyclecount.R
import com.unis.cyclecount.core.CycleCountEvent
import com.unis.cyclecount.core.newitem.CcNewItemWorkFragment
import com.unis.cyclecount.core.newitem.newitemsubmit.CcNewItemSubmitFragment
import com.unis.cyclecount.core.work.CcCycleCountWorkFragment
import com.unis.cyclecount.core.work.Page
import com.unis.cyclecount.core.work.scanitem.CcScanItemFragment
import com.unis.cyclecount.core.work.scanlp.CcScanLpFragment
import com.unis.cyclecount.util.CcDatePickerDialog
import com.unis.platform.cyclecount.model.CountMethod
import com.unis.platform.location_v2.model.LocationType
import com.unis.reactivemvi.mvi.ReactiveFragment
import com.unis.reactivemvi.mvi.ReactiveViewScope
import com.unis.reactivemvi.mvi.onEvent
import com.unis.reactivemvi.mvvm.kotlin.extensions.getParentFragmentViewModel
import com.unis.reactivemvi.mvvm.kotlin.extensions.hideKeyboard
import com.unis.reactivemvi.mvvm.kotlin.extensions.newFragmentInstance
import com.unis.reactivemvi.mvvm.kotlin.extensions.showKeyboard
import java.lang.Math.floor
import java.util.*

class CcCountItemFragment : ReactiveFragment<CountItemViewModel, CountItemUiState, FragmentCcCountItemBinding>() {

    companion object {
        const val TAG = "CcCountItemFragment"
        const val IS_NEW_ITEM = "isNewItem"
        fun newInstance(isNewItem: Boolean) = newFragmentInstance<CcCountItemFragment>(IS_NEW_ITEM to isNewItem)
    }

    private val parentViewModelForCycleCountWork by lazy { getParentFragmentViewModel<CycleCountWorkViewModel>()!! }

    private val parentViewModelForNewItemWork by lazy { parentFragment?.getParentFragmentViewModel<NewItemWorkViewModel>()!! }

    override fun createViewModel(): CountItemViewModel {
        val isNewItem = getBundleObject<Boolean>(IS_NEW_ITEM)
        val rootParentViewModel = if (isNewItem) { parentViewModelForNewItemWork } else parentViewModelForCycleCountWork
        return CountItemViewModel(
                isNewItem = isNewItem,
                rootParentViewModel = rootParentViewModel,
        )
    }

    override fun initView(savedInstanceState: Bundle?) {
        binding?.apply {
            calculatorTv.setVisible()
            val isPickLocation = viewModel.getCurrentLocation()?.type == LocationType.PICK
            val isShowLp = !isPickLocation && !TextUtils.isEmpty(viewModel.getCurrentLp())
            if (viewModel.isNewItem()) {
                lpLl.setGone()
                itemLl.setGone()
                lpIdForNewItemLl.setVisibleOrGone(!isPickLocation)
                locationForNewItemLl.setVisible()
                itemForNewItemLl.setVisible()
                lpIdForNewItemTv.text = if (!isPickLocation) viewModel.getCurrentLp() else ""
                locationNameForNewItemTv.text = viewModel.getCurrentLocation()?.name
                itemSpecForNewItemTv.text = viewModel.getCurrentItemSpec()?.name
                calculatorFl.setGone()
            } else {
                lpLl.setVisibleOrGone(isShowLp)
                lpIdTv.text = if (isShowLp) viewModel.getCurrentLp() else ""
                itemSpecTv.text = viewModel.getCurrentItemSpec()?.name
                setMoreAndUomTransferVisible()
            }
            if (!viewModel.isNewItem() && !viewModel.isBlind() && !viewModel.getRequireCountQtys().isNullOrEmpty()) {
                requireCountQtyLl.setVisible()
                requireCountQtyTv.text = viewModel.getRequireCountQtys()?.joinToString(separator = " | ")
            }
            if (viewModel.isNewItem() && viewModel.isRequireCollectLotNo()) {
                lotNoScanner.setVisible()
            }
            if (viewModel.isNewItem() && viewModel.isRequireCollectExpirationDate()) {
                expirationDateLayout.setVisible()
            }
            if (viewModel.isNewItem() && viewModel.isRequireCollectMfgDate()) {
                mfgDateLayout.setVisible()
            }
            if (viewModel.isNewItem() && viewModel.isRequireCollectShelfLifeDaysDate()) {
                shelfLifeDaysLayout.setVisible()
            }
            lotNoScanner.setScanEvent { _, data ->
                lotNoScanner.text = data
                viewModel.saveLotNo(data)
            }
            qtyEdit.postDelayed({
                showKeyboard()
            }, 300)
            qtyEdit.requestFocus()
            qtyEdit.addTextChangedListener(object : TextWatcher {
                override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {
                }

                override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {
                }

                override fun afterTextChanged(s: Editable?) {
                    viewModel.saveQty(s.toString())
                }

            })
            shelfLifeDaysEdit.addTextChangedListener(object : TextWatcher {
                override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {
                }

                override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {
                }

                override fun afterTextChanged(s: Editable?) {
                    viewModel.saveShelfLifeDays(s.toString())
                }

            })
            uomTv.setOnClickListener { showUomSelectDialog() }
            calculatorTv.setOnClickListener { showCalculator() }
            moreTv.setOnClickListener { showUomTransfer() }
            expirationDateEdit.setOnClickListener { showExpirationDatePicker() }
            mfgDateEdit.setOnClickListener { showMfgDatePicker() }
            enterBtn.setOnClickListener {
                hideKeyboard()
                viewModel.submitCounted(!viewModel.isNewItem())
            }
        }
        onRecountAndOverrideLotNoDialogEvent()
        onAddLotNoInventoryDialogEvent()
        onReRenderViewEvent()
        onReCycleCountEvent()
        showNoMatchSimpleQtyCountDialog()
        setNextLocationAndSubmitVisible()
        onShowCloseTaskDialogEvent()
        onClosedTaskEvent()
        onShowItemLpsDialog()
    }

    private fun setMoreAndUomTransferVisible() {
        binding?.apply {
            if (viewModel.isBaseAndOnlyOneUnit()) {
                moreTv.setGone()
                uomTranslateTv.setGone()
            }
        }
    }

    private fun showUomTransfer() {
        val baseUnit = viewModel.getBaseUnit()
        if (baseUnit != null) {
            val filteredList = viewModel.getUnitOptions().filter { v -> !v.isBaseUom }
            val uomTransferAdapter = CcUomTransferAdapter(baseUnit)
            uomTransferAdapter.setDiffList(filteredList)
            val dialog = UomTransferDialog(uomTransferAdapter)
            dialog.show(fragmentManager, "uomTransferDialog")
        }
    }

    private fun showUomSelectDialog() {
        context?: return
        if (viewModel.getUnitOptions().isEmpty()) return
        CenterDialog.singleChoiceList(
                context = context!!,
                items = viewModel.getUnitOptions(),
                itemTitleMapper = {
                    it.name
                },
                defaultChose = viewModel.getSelectedUnit(),
                positiveClick = {
                    viewModel.setSelectedUnit(it!!)
                }
        ).show()
    }

    private fun setNextLocationAndSubmitVisible() {
        if (viewModel.countMethod == CountMethod.SIMPLE_QTY_COUNT && !viewModel.isNewItem()){
            (parentFragment as CcCycleCountWorkFragment).setNextLocationBtnVisible(true)
            (parentFragment as CcCycleCountWorkFragment).setSubmitBtnVisible(false)
        }
    }

    private fun showCalculator() {
        CommUtil.startCalculator(activity)
    }

    private fun showExpirationDatePicker() {
        context?: return
        CcDatePickerDialog(
                context = context!!,
                titleText = getString(R.string.title_select_expiration_date),
                defaultDate = viewModel.getExpirationDate(),
                onDateSelected = { viewModel.saveExpirationDate(it) }
        ).show()
    }

    private fun showMfgDatePicker() {
        context?: return
        CcDatePickerDialog(
                context = context!!,
                titleText = getString(R.string.title_select_mfg_date),
                defaultDate = viewModel.getMfgDate(),
                onDateSelected = { viewModel.saveMfgDate(it) }
        ).show()
    }


    override fun ReactiveViewScope.subscribeToUiState() {
        showUnit()
        countItemComposeChange()
    }

    private fun ReactiveViewScope.showUnit() =
            subscribe(CountItemUiState::selectedItemUomEntity) {
                binding?.apply {
                    it?.let {
                        uomTv.text = it.name
                        val baseUom = viewModel.getBaseUnit()
                        if (baseUom != null && !it.isBaseUom) {
                            uomLl.setVisible()
                            uomTranslateTv.text = "*1 ${it.name} ${activity?.getString(R.string.text_pack)} ${floor(it.baseQty).toInt()} ${baseUom!!.name}"
                        } else {
                            uomLl.setGone()
                        }
                    }
                }
            }

    private fun ReactiveViewScope.countItemComposeChange() =
            subscribe(CountItemUiState::countItemCompose) {
                binding?.apply {
                    it?.let {
                        expirationDateEdit.setText(it.expirationDate.format())
                        mfgDateEdit.setText(it.mfgDate.format())
                        enterBtn.isEnabled = isEnableEnterBtn(it)
                    }
                }
            }

    private fun isEnableEnterBtn(countItemCompose: CountItemCompose): Boolean {
        return when {
            countItemCompose.qty.isNullOrEmpty() -> false
            viewModel.isNewItem() && viewModel.isRequireCollectLotNo() && countItemCompose.lotNo.isNullOrEmpty() -> false
            viewModel.isNewItem() && viewModel.isRequireCollectExpirationDate() && countItemCompose.expirationDate == null -> false
            viewModel.isNewItem() && viewModel.isRequireCollectMfgDate() && countItemCompose.mfgDate == null -> false
            viewModel.isNewItem() && viewModel.isRequireCollectShelfLifeDaysDate() && countItemCompose.shelfLifeDays.isNullOrEmpty() -> false
            else -> true
        }
    }

    private fun Date?.format(): CharSequence? {
        this ?: return null
        return DateFormat.format("MM/dd/yy", this)
    }

    private fun onRecountAndOverrideLotNoDialogEvent() = onEvent<CountItemEvent.RecountAndOverrideLotNoDialog> {
        context?: return@onEvent
        CenterDialog.confirm(
                context = context!!,
                message = msg,
                positiveClick = {
                    viewModel.submitOverride()
                }
        ).show()
    }

    private fun onAddLotNoInventoryDialogEvent() = onEvent<CountItemEvent.AddLotNoInventoryDialog> {
        context?: return@onEvent
        CenterDialog.confirm(
                context = context!!,
                message = msg,
                positiveClick = {
//                    viewModel.addLotNoInventory()
                }
        ).show()
    }

    private fun onReRenderViewEvent() = onEvent<CountItemEvent.ReRenderView> {
        when (page) {
            Page.ScanLp -> {
                val scanLpFragment = parentFragment?.childFragmentManager?.findFragmentByTag(
                    CcScanLpFragment.TAG) as CcScanLpFragment
                scanLpFragment.onReRenderView()
            }
            Page.ScanItem -> {
                val scanItemFragment = parentFragment?.childFragmentManager?.findFragmentByTag(
                    CcScanItemFragment.TAG) as CcScanItemFragment
                scanItemFragment.onReRenderView()
            }
            Page.ScanLocation -> {}
            Page.CountItem -> {}
        }
    }

    private fun onReCycleCountEvent() = onEvent<CountItemEvent.ReCycleCount> {
        binding?.apply {
            qtyEdit.setText("")
        }
    }

    private fun showNoMatchSimpleQtyCountDialog() = onEvent<CountItemEvent.ShowNoMatchSimpleQtyCountDialog> {
        context?: return@onEvent
        if (isPickLocation) {
            showNoMatchSimpleQtyCountDialogForPickLocation()
        } else {
            showNoMatchSimpleQtyCountDialogForNotPickLocation()
        }
    }

    private fun onShowCloseTaskDialogEvent() = onEvent<CountItemEvent.ShowCloseTaskDialog> {
        context ?: return@onEvent
        CenterDialog.confirm(
            context = context!!,
            message = msg,
            negativeClick = {
                viewModel.handleAfterSubmit(itemId)
            },
            positiveClick = {
                viewModel.closeTask()
            }
        ).show()
    }

    private fun onClosedTaskEvent() = onEvent<CountItemEvent.ClosedTask> {
        activity?.finish()
    }

    private fun onShowItemLpsDialog() = onEvent<CountItemEvent.ShowItemLpsDialog> {
        context ?: return@onEvent
        
        val message = if (countInventories.size > 5) {
            String.format(getString(R.string.msg_item_exists_in_lps), countInventories.first().itemName, countInventories.subList(0, 5).joinToString("\n") { it.lpId } + "...")
        } else {
            String.format(getString(R.string.msg_item_exists_in_lps), countInventories.first().itemName, countInventories.joinToString("\n") { it.lpId })
        }
        
        CenterDialog.confirm(
            context = context!!,
            message = message,
            positiveText = getString(R.string.btn_confirm),
            positiveClick = {
                viewModel.onStartSubmit(true)
            }
        ).show()
    }

    private fun showNoMatchSimpleQtyCountDialogForPickLocation() {
        context?: return
        CenterDialog.alert(
            context = context!!,
            message = getString(R.string.msg_no_match_qty_count),
            okClick = {
                viewModel.setIsRecount(true)
            }
        ).show()
    }

    private fun showNoMatchSimpleQtyCountDialogForNotPickLocation() {
        context?: return
        CenterDialog.confirm(
            context = context!!,
            message = getString(R.string.msg_no_match_simple_qty_count),
            positiveText = getString(R.string.btn_continue),
            positiveClick = {
                viewModel.setIsRecount(true)
                viewModel.submitCounted(false)
            }
        ).show()
    }


}