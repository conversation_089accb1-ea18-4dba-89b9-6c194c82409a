<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:orientation="vertical"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/page_background_v1">

    <LinearLayout
        android:id="@+id/require_count_qty_ll"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:paddingHorizontal="32dp"
        android:paddingVertical="10dp"
        android:background="@color/colorBgDark"
        android:visibility="gone">

        <androidx.appcompat.widget.AppCompatTextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/label_require_count_qty"
            android:textSize="@dimen/text_size_body3_v1"
            android:textColor="@color/colorTextSecondary"/>

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/require_count_qty_tv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="10dp"
            tools:text="1EA"
            android:textSize="@dimen/text_size_body3_v1"
            android:textColor="@color/colorAccent"/>

    </LinearLayout>

    <LinearLayout
        android:id="@+id/lp_ll"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:paddingHorizontal="32dp"
        android:paddingVertical="10dp"
        android:background="@color/colorBgDark">

        <androidx.appcompat.widget.AppCompatTextView
            android:layout_width="60dp"
            android:layout_height="wrap_content"
            android:text="@string/label_lp_colon"
            android:textSize="@dimen/text_size_body3_v1"
            android:textColor="@color/colorTextSecondary"/>

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/lp_id_tv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            tools:text="ILP-13333"
            android:textSize="@dimen/text_size_body3_v1"
            android:textColor="@color/colorTextPrimary"/>

    </LinearLayout>
    <LinearLayout
        android:id="@+id/item_ll"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:paddingHorizontal="32dp"
        android:background="@color/colorBgDark"
        android:paddingVertical="10dp"
        android:gravity="center_vertical">

        <androidx.appcompat.widget.AppCompatTextView
            android:layout_width="60dp"
            android:layout_height="wrap_content"
            android:text="@string/label_item_colon"
            android:textSize="@dimen/text_size_body3_v1"
            android:textColor="@color/colorTextSecondary"/>

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/item_spec_tv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            tools:text="Item-1"
            android:textSize="@dimen/text_size_body3_v1"
            android:textColor="@color/colorTextPrimary"/>

    </LinearLayout>

    <LinearLayout
        android:id="@+id/location_for_new_item_ll"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:paddingHorizontal="32dp"
        android:paddingVertical="10dp"
        android:background="@color/colorBgDark"
        android:visibility="gone">

        <androidx.appcompat.widget.AppCompatTextView
            android:layout_width="60dp"
            android:layout_height="wrap_content"
            android:text="@string/label_location_mark"
            android:textSize="@dimen/text_size_body3_v1"
            android:textColor="@color/colorTextSecondary"/>

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/location_name_for_new_item_tv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            tools:text="021.01"
            android:textSize="@dimen/text_size_body3_v1"
            android:textColor="@color/colorTextPrimary"/>

    </LinearLayout>
    <LinearLayout
        android:id="@+id/item_for_new_item_ll"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:paddingHorizontal="32dp"
        android:paddingBottom="10dp"
        android:background="@color/colorBgDark"
        android:visibility="gone">

        <androidx.appcompat.widget.AppCompatTextView
            android:layout_width="60dp"
            android:layout_height="wrap_content"
            android:text="@string/label_item_colon"
            android:textSize="@dimen/text_size_body3_v1"
            android:textColor="@color/colorTextSecondary"/>

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/item_spec_for_new_item_tv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            tools:text="Item-1"
            android:textSize="@dimen/text_size_body3_v1"
            android:textColor="@color/colorTextPrimary"/>

    </LinearLayout>

    <LinearLayout
        android:id="@+id/lp_id_for_new_item_ll"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:paddingHorizontal="32dp"
        android:paddingBottom="10dp"
        android:background="@color/colorBgDark"
        android:visibility="gone">

        <androidx.appcompat.widget.AppCompatTextView
            android:layout_width="60dp"
            android:layout_height="wrap_content"
            android:text="@string/label_lp_colon"
            android:textSize="@dimen/text_size_body3_v1"
            android:textColor="@color/colorTextSecondary"/>

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/lp_id_for_new_item_tv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            tools:text="ILP-1"
            android:textSize="@dimen/text_size_body3_v1"
            android:textColor="@color/colorTextPrimary"/>

    </LinearLayout>

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <FrameLayout
                android:id="@+id/calculator_fl"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="20dp"
                android:layout_marginHorizontal="@dimen/page_horizontal_margin_v1">

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/calculator_tv"
                    style="@style/text_blue_14sp"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:visibility="gone"
                    tools:visibility="visible"
                    android:textAllCaps="true"
                    android:text="@string/text_calculator"/>

                <LinearLayout
                    android:id="@+id/uom_ll"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:layout_gravity="end">

                    <androidx.appcompat.widget.AppCompatTextView
                        android:id="@+id/uom_translate_tv"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:textColor="@color/colorTextPrimary"
                        android:textSize="@dimen/text_size_body4_v1"
                        tools:text="* 1CS Pack 32EA"/>

                    <androidx.appcompat.widget.AppCompatTextView
                        android:id="@+id/more_tv"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="5dp"
                        android:textColor="@color/colorAccent"
                        android:textSize="@dimen/text_size_body4_v1"
                        android:text="@string/nav_title_more"/>
                </LinearLayout>

            </FrameLayout>

            <com.google.android.material.textfield.TextInputLayout
                android:id="@+id/title_layout"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:minHeight="56dp"
                android:background="@drawable/rect_bg_input_8"
                android:layout_marginTop="12dp"
                android:paddingTop="8dp"
                android:paddingBottom="16dp"
                android:layout_marginHorizontal="@dimen/page_horizontal_margin_v1"
                app:hintTextAppearance="@style/textLayoutHintAppearance"
                app:hintTextColor="@color/text_hint_v1"
                android:textColorHint="@color/text_hint_v1"
                android:visibility="gone"
                tools:visibility="visible">

                <androidx.appcompat.widget.AppCompatEditText
                    android:id="@+id/title_edit"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="@android:color/transparent"
                    android:drawableEnd="@drawable/ic_arrow_drop_down_white"
                    android:paddingStart="16dp"
                    android:paddingEnd="16dp"
                    android:focusable="false"
                    android:inputType="number"
                    android:hint="@string/text_title"
                    android:textSize="@dimen/text_size_body3_v1"
                    android:textColor="@color/colorTextPrimary"
                    android:textColorHint="@color/text_hint_v1"
                    tools:text="Title"
                    />

            </com.google.android.material.textfield.TextInputLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:layout_marginTop="12dp"
                android:layout_marginHorizontal="@dimen/page_horizontal_margin_v1">

                <com.google.android.material.textfield.TextInputLayout
                    android:id="@+id/qty_layout"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:minHeight="56dp"
                    android:background="@drawable/rect_bg_input_8"
                    android:paddingTop="8dp"
                    android:paddingBottom="16dp"
                    app:hintTextAppearance="@style/textLayoutHintAppearance"
                    app:hintTextColor="@color/text_hint_v1"
                    android:textColorHint="@color/text_hint_v1">

                    <androidx.appcompat.widget.AppCompatEditText
                        android:id="@+id/qty_edit"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:background="@android:color/transparent"
                        android:paddingStart="16dp"
                        android:paddingEnd="16dp"
                        android:inputType="number"
                        android:hint="@string/hint_quantity"
                        android:textSize="@dimen/text_size_body3_v1"
                        android:textColor="@color/colorTextPrimary"
                        android:textColorHint="@color/text_hint_v1"
                        tools:text="123"
                        />

                </com.google.android.material.textfield.TextInputLayout>

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/uom_tv"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:layout_marginStart="10dp"
                    android:paddingHorizontal="10dp"
                    android:background="@drawable/rect_bg_input_8"
                    android:drawablePadding="5dp"
                    android:drawableEnd="@drawable/ic_arrow_drop_down_white"
                    android:gravity="center"
                    android:textSize="16sp"
                    android:textColor="@color/colorTextPrimary"
                    tools:text="EA"/>

            </LinearLayout>

            <com.customer.widget.QuickScanner
                android:id="@+id/lot_no_scanner"
                android:layout_width="match_parent"
                android:layout_height="56dp"
                android:layout_marginTop="12dp"
                android:layout_marginHorizontal="@dimen/page_horizontal_margin_v1"
                android:gravity="center_vertical"
                android:background="@drawable/rect_bg_input_8"
                app:hintTextSize="14sp"
                app:hintText="@string/hint_lot"
                app:hintTextColor="@color/text_hint_v1"
                app:layout_res="@layout/view_quick_scanner_v1_purple_icon"
                android:visibility="gone"
                tools:visibility="visible"/>

            <com.google.android.material.textfield.TextInputLayout
                android:id="@+id/expiration_date_layout"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:minHeight="56dp"
                android:background="@drawable/rect_bg_input_8"
                android:layout_marginTop="12dp"
                android:paddingTop="8dp"
                android:paddingBottom="16dp"
                android:layout_marginHorizontal="@dimen/page_horizontal_margin_v1"
                app:hintTextAppearance="@style/textLayoutHintAppearance"
                app:hintTextColor="@color/text_hint_v1"
                android:textColorHint="@color/text_hint_v1"
                android:visibility="gone"
                tools:visibility="visible">

                <androidx.appcompat.widget.AppCompatEditText
                    android:id="@+id/expiration_date_edit"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="@android:color/transparent"
                    android:drawableEnd="@drawable/ic_date_gray"
                    android:paddingStart="16dp"
                    android:paddingEnd="16dp"
                    android:focusable="false"
                    android:inputType="number"
                    android:hint="@string/expiration_date_colon"
                    android:textSize="@dimen/text_size_body3_v1"
                    android:textColor="@color/colorTextPrimary"
                    android:textColorHint="@color/text_hint_v1"
                    tools:text="2024-06-06"
                    />

            </com.google.android.material.textfield.TextInputLayout>

            <com.google.android.material.textfield.TextInputLayout
                android:id="@+id/mfg_date_layout"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:minHeight="56dp"
                android:background="@drawable/rect_bg_input_8"
                android:layout_marginTop="12dp"
                android:paddingTop="8dp"
                android:paddingBottom="16dp"
                android:layout_marginHorizontal="@dimen/page_horizontal_margin_v1"
                app:hintTextAppearance="@style/textLayoutHintAppearance"
                app:hintTextColor="@color/text_hint_v1"
                android:textColorHint="@color/text_hint_v1"
                android:visibility="gone"
                tools:visibility="visible">

                <androidx.appcompat.widget.AppCompatEditText
                    android:id="@+id/mfg_date_edit"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="@android:color/transparent"
                    android:drawableEnd="@drawable/ic_date_gray"
                    android:paddingStart="16dp"
                    android:paddingEnd="16dp"
                    android:focusable="false"
                    android:inputType="number"
                    android:hint="@string/hint_mfg_date"
                    android:textSize="@dimen/text_size_body3_v1"
                    android:textColor="@color/colorTextPrimary"
                    android:textColorHint="@color/text_hint_v1"
                    tools:text="2024-06-06"
                    />

            </com.google.android.material.textfield.TextInputLayout>

            <com.google.android.material.textfield.TextInputLayout
                android:id="@+id/shelf_life_days_layout"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:minHeight="56dp"
                android:background="@drawable/rect_bg_input_8"
                android:layout_marginTop="12dp"
                android:paddingTop="8dp"
                android:paddingBottom="16dp"
                android:layout_marginHorizontal="@dimen/page_horizontal_margin_v1"
                app:hintTextAppearance="@style/textLayoutHintAppearance"
                app:hintTextColor="@color/text_hint_v1"
                android:textColorHint="@color/text_hint_v1"
                android:visibility="gone"
                tools:visibility="visible">

                <androidx.appcompat.widget.AppCompatEditText
                    android:id="@+id/shelf_life_days_edit"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="@android:color/transparent"
                    android:paddingStart="16dp"
                    android:paddingEnd="16dp"
                    android:inputType="number"
                    android:hint="@string/self_life_days"
                    android:textSize="@dimen/text_size_body3_v1"
                    android:textColor="@color/colorTextPrimary"
                    android:textColorHint="@color/text_hint_v1"
                    tools:text="123"
                    />

            </com.google.android.material.textfield.TextInputLayout>

            <com.customer.widget.StateButton
                android:id="@+id/enter_btn"
                style="@style/raisedButtonStyle"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="10dp"
                android:text="@string/text_enter"
                android:textColor="@color/white"
                android:textSize="14sp"
                android:enabled="false"
                android:layout_marginTop="12dp"
                android:layout_marginHorizontal="@dimen/page_horizontal_margin_v1"
                app:btnradius="4dp"
                app:normalBackgroundColor="@color/colorAccent"
                app:normalTextColor="@color/white"
                app:pressedBackgroundColor="@color/colorAccent"
                app:pressedTextColor="@color/colorTextPrimary"
                app:unableBackgroundColor="@color/colorAccent_o40"
                app:unableTextColor="@color/color_disable"/>

        </LinearLayout>
    </androidx.core.widget.NestedScrollView>

</LinearLayout>